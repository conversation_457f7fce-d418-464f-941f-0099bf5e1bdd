
-- RDS Proxy IAM Authentication Fix
--
-- Problem: RDS Proxy IAM auth fails with "This RDS proxy has no credentials for the role common_user"
-- Root Cause: The IAM auth secret contains master user credentials (Allerium) instead of common_user
-- Solution: Create proper secret for IAM auth and update Terraform configuration

-- 1. Drop the existing user if it exists (it might have been created incorrectly)
DROP USER IF EXISTS common_user;

-- 2. Create user for IAM authentication (no password needed for IAM auth)
CREATE USER common_user;

-- 3. Grant the rds_iam role to enable IAM authentication
GRANT rds_iam TO common_user;

-- 4. Create schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS common;

-- 5. Grant schema usage permissions for 'common' schema
GRANT USAGE ON SCHEMA "common" TO common_user;

-- 6. Grant table permissions for existing tables in 'common' schema
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA "common" TO common_user;

-- 7. Grant sequence permissions for existing sequences in 'common' schema
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA "common" TO common_user;

-- 8. Set default privileges for future tables in 'common' schema
ALTER DEFAULT PRIVILEGES IN SCHEMA "common"
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO common_user;

-- 9. Set default privileges for future sequences in 'common' schema
ALTER DEFAULT PRIVILEGES IN SCHEMA "common"
GRANT USAGE, SELECT ON SEQUENCES TO common_user;

-- 10. Grant EXECUTE permissions on stored procedures/functions in 'common' schema
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA "common" TO common_user;

-- 11. Set default privileges for future functions in 'common' schema
ALTER DEFAULT PRIVILEGES IN SCHEMA "common"
GRANT EXECUTE ON FUNCTIONS TO common_user;

-- 12. Grant access to other schemas if needed (memo, public, etc.)
GRANT USAGE ON SCHEMA "memo" TO common_user;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA "memo" TO common_user;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA "memo" TO common_user;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA "memo" TO common_user;

ALTER DEFAULT PRIVILEGES IN SCHEMA "memo"
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO common_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA "memo"
GRANT USAGE, SELECT ON SEQUENCES TO common_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA "memo"
GRANT EXECUTE ON FUNCTIONS TO common_user;

-- 13. Verify the user was created correctly
SELECT usename, usesuper, usecreatedb
FROM pg_user
WHERE usename = 'common_user';

-- 14. Verify IAM role assignment
SELECT r.rolname
FROM pg_roles r
JOIN pg_auth_members m ON r.oid = m.roleid
JOIN pg_roles u ON m.member = u.oid
WHERE u.rolname = 'common_user' AND r.rolname = 'rds_iam';