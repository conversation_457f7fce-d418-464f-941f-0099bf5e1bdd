
-- 1. Drop the existing user if it exists (it might have been created incorrectly)
DROP USER IF EXISTS common_user;

-- 2. Create user for IAM authentication (no password needed)
CREATE USER common_user;
CREATE SCHEMA common;

-- 3. Grant the rds_iam role to enable IAM authentication
GRANT rds_iam TO common_user;

-- 4. <PERSON> schema usage permissions for 'common' schema
GRANT USAGE ON SCHEMA "common" TO common_user;

-- 5. Grant table permissions for existing tables in 'common' schema
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA "common" TO common_user;

-- 6. Grant sequence permissions for existing sequences in 'common' schema
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA "common" TO common_user;

-- 7. Set default privileges for future tables in 'common' schema
ALTER DEFAULT PRIVILEGES IN SCHEMA "common" 
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO common_user;

-- 8. Set default privileges for future sequences in 'common' schema
ALTER DEFAULT PRIVILEGES IN SCHEMA "common"
GRANT USAGE, SELECT ON SEQUENCES TO common_user;

-- 9. <PERSON> EXECUTE permissions on stored procedures/functions in 'common' schema
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA "common" TO common_user;

-- 10. Set default privileges for future functions in 'common' schema
ALTER DEFAULT PRIVILEGES IN SCHEMA "common"
GRANT EXECUTE ON FUNCTIONS TO common_user;

-- 11. Verify the user was created correctly
SELECT usename, usesuper, usecreatedb 
FROM pg_user 
WHERE usename = 'common_user';