#!/usr/bin/env python3
"""Test RDS Proxy IAM authentication with master user."""

import asyncio
import sys

import asyncpg
import boto3


async def test_proxy_master():
    """Test connection to RDS Proxy with master user IAM."""
    print("🔧 Testing RDS Proxy IAM authentication with master user...")
    
    # Generate fresh IAM token for master user
    rds_client = boto3.client("rds", region_name="ap-southeast-2")
    
    host = "dev-au-smartanalytics-common-aurora-db-proxy.proxy-c3atdbtyiqrw.ap-southeast-2.rds.amazonaws.com"
    port = 5432
    database = "prod"
    username = "Allerium"  # Master user
    
    print(f"   Host: {host}")
    print(f"   Database: {database}")
    print(f"   Username: {username} (master user)")
    
    try:
        # Generate fresh IAM token for master user
        print("   Generating IAM token for master user...")
        iam_token = rds_client.generate_db_auth_token(
            DBHostname=host,
            Port=port,
            DBUsername=username,
            Region="ap-southeast-2"
        )
        
        print(f"✅ IAM token generated! Length: {len(iam_token)}")
        print(f"   Token preview: {iam_token[:50]}...")
        
        # Connect using IAM token
        print("   Connecting with IAM token...")
        conn = await asyncpg.connect(
            host=host,
            port=port,
            database=database,
            user=username,
            password=iam_token,
            ssl="require",
            command_timeout=30
        )
        
        print("✅ Connected successfully!")
        
        # Simple test query
        result = await conn.fetchval("SELECT 1")
        print(f"✅ Query successful! Result: {result}")
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Master user IAM authentication failed: {e}")
        print(f"   Error type: {type(e).__name__}")
        return False


async def main():
    """Main function."""
    print("🚀 Testing RDS Proxy IAM Authentication with Master User")
    print("=" * 70)
    
    success = await test_proxy_master()
    
    if success:
        print("\n✅ Master user IAM authentication works!")
        print("   The issue is with the common_user mapping.")
        return 0
    else:
        print("\n❌ Master user IAM authentication also failing.")
        print("   The issue is with the proxy IAM configuration.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
