"""Unit tests for Lambda handler."""

import json
import os
from typing import Any, cast
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from aws_lambda_typing.events.sqs import SQSMessage

from src.lambda_function import _process_events_async, lambda_handler

# Set environment variables before importing
os.environ.update(
    {
        "DATABASE_HOST": "test-host",
        "DATABASE_DATABASE_NAME": "test-db",
        "DATABASE_USERNAME": "test-user",
        "DATABASE_PASSWORD": "test-pass",
        "DATABASE_SCHEMA_NAME": "test_schema",
        "ENVIRONMENT": "local",
    }
)


def _build_sqs_record(
    message_id: str, body: str = "<LogEvent></LogEvent>"
) -> SQSMessage:
    """Return a realistic SQSMessage TypedDict for tests."""
    record: dict[str, Any] = {
        "messageId": message_id,
        "receiptHandle": "test-receipt",
        "body": body,
        "attributes": {
            "ApproximateReceiveCount": "1",
            "SentTimestamp": "0",
            "SenderId": "000000000000",
            "ApproximateFirstReceiveTimestamp": "0",
        },
        "messageAttributes": {},
        "md5OfBody": "d41d8cd98f00b204e9800998ecf8427e",
        "eventSource": "aws:sqs",
        "eventSourceARN": "arn:aws:sqs:us-east-1:123456789012:test-queue",
        "awsRegion": "us-east-1",
    }
    return cast("SQSMessage", record)


class MockLambdaContext:
    """Mock Lambda context for testing."""

    def __init__(self):
        self.function_name = "test-function"
        self.function_version = "$LATEST"
        self.invoked_function_arn = (
            "arn:aws:lambda:us-east-1:123456789012:function:test"
        )
        self.memory_limit_in_mb = 512
        self.request_id = "test-request-id"
        self.aws_request_id = "test-request-id"
        self.log_group_name = "/aws/lambda/test"
        self.log_stream_name = "2025/01/01/test"
        self.identity = None
        self.client_context = None

    def get_remaining_time_in_millis(self):
        return 300000


class TestProcessEventsAsync:
    """Test async event processing."""

    @pytest.mark.asyncio
    async def test_process_single_event_success(self):
        """Test processing single event successfully."""
        xml_contents = ["<LogEvent><eventType>Test</eventType></LogEvent>"]
        sqs_message_ids = ["msg-1"]
        sqs_records: list[SQSMessage] = [_build_sqs_record("msg-1", xml_contents[0])]

        mock_result = MagicMock()
        mock_result.success = True
        mock_result.is_duplicate = False
        mock_result.event_hash = "abc123"
        mock_result.processing_time_ms = 100.0

        with patch(
            "src.lambda_function.ProcessAgentEventCommandHandler"
        ) as mock_handler_class:
            mock_handler = AsyncMock()
            mock_handler.handle = AsyncMock(return_value=mock_result)
            mock_handler_class.return_value = mock_handler

            with patch("src.lambda_function.get_async_session"):
                result = await _process_events_async(
                    xml_contents, sqs_message_ids, sqs_records
                )

                assert result["total_events"] == 1
                assert result["processed_events"] == 1
                assert result["duplicate_events"] == 0
                assert result["failed_events"] == 0
                assert len(result["event_hashes"]) == 1

    @pytest.mark.asyncio
    async def test_process_single_event_duplicate(self):
        """Test processing duplicate event."""
        xml_contents = ["<LogEvent><eventType>Test</eventType></LogEvent>"]
        sqs_message_ids = ["msg-1"]
        sqs_records: list[SQSMessage] = [_build_sqs_record("msg-1", xml_contents[0])]

        mock_result = MagicMock()
        mock_result.success = True
        mock_result.is_duplicate = True
        mock_result.event_hash = "abc123"
        mock_result.processing_time_ms = 50.0

        with patch(
            "src.lambda_function.ProcessAgentEventCommandHandler"
        ) as mock_handler_class:
            mock_handler = AsyncMock()
            mock_handler.handle = AsyncMock(return_value=mock_result)
            mock_handler_class.return_value = mock_handler

            with patch("src.lambda_function.get_async_session"):
                result = await _process_events_async(
                    xml_contents, sqs_message_ids, sqs_records
                )

                assert result["processed_events"] == 0
                assert result["duplicate_events"] == 1

    @pytest.mark.asyncio
    async def test_process_single_event_failure(self):
        """Test processing failed event."""
        xml_contents = ["<LogEvent><eventType>Test</eventType></LogEvent>"]
        sqs_message_ids = ["msg-1"]
        sqs_records: list[SQSMessage] = [_build_sqs_record("msg-1", xml_contents[0])]

        mock_result = MagicMock()
        mock_result.success = False
        mock_result.is_duplicate = False
        mock_result.event_hash = None
        mock_result.processing_time_ms = 75.0

        with patch(
            "src.lambda_function.ProcessAgentEventCommandHandler"
        ) as mock_handler_class:
            mock_handler = AsyncMock()
            mock_handler.handle = AsyncMock(return_value=mock_result)
            mock_handler_class.return_value = mock_handler

            with patch("src.lambda_function.get_async_session"):
                result = await _process_events_async(
                    xml_contents, sqs_message_ids, sqs_records
                )

                assert result["processed_events"] == 0
                assert result["failed_events"] == 1
                assert len(result["event_hashes"]) == 0

    @pytest.mark.asyncio
    async def test_process_batch_events(self):
        """Test processing batch of events."""
        xml_contents = [
            "<LogEvent><eventType>Test1</eventType></LogEvent>",
            "<LogEvent><eventType>Test2</eventType></LogEvent>",
        ]
        sqs_message_ids = ["msg-1", "msg-2"]
        sqs_records: list[SQSMessage] = [
            _build_sqs_record("msg-1", xml_contents[0]),
            _build_sqs_record("msg-2", xml_contents[1]),
        ]

        mock_result = MagicMock()
        mock_result.total_events = 2
        mock_result.processed_events = 2
        mock_result.duplicate_events = 0
        mock_result.failed_events = 0
        mock_result.event_hashes = ["abc123", "def456"]
        mock_result.batch_id = "batch-123"
        mock_result.processing_time_ms = 200.0

        with patch(
            "src.lambda_function.ProcessAgentEventsBatchCommandHandler"
        ) as mock_handler_class:
            mock_handler = AsyncMock()
            mock_handler.handle = AsyncMock(return_value=mock_result)
            mock_handler_class.return_value = mock_handler

            with patch("src.lambda_function.get_async_session"):
                result = await _process_events_async(
                    xml_contents, sqs_message_ids, sqs_records
                )

                assert result["total_events"] == 2
                assert result["processed_events"] == 2
                assert result["batch_id"] == "batch-123"


class TestLambdaHandler:
    """Test Lambda handler function."""

    def test_handler_success(self):
        """Test handler with successful event processing."""
        event = {
            "Records": [
                {
                    "messageId": "msg-1",
                    "body": "<LogEvent><eventType>Test</eventType></LogEvent>",
                }
            ]
        }
        context = MockLambdaContext()

        mock_result = {
            "total_events": 1,
            "processed_events": 1,
            "duplicate_events": 0,
            "failed_events": 0,
            "event_hashes": ["abc123"],
            "processing_time_ms": 100.0,
        }

        with patch(
            "src.lambda_function._process_events_async", new_callable=AsyncMock
        ) as mock_process:
            mock_process.return_value = mock_result

            response = lambda_handler(event, context)

            assert response["statusCode"] == 200
            body = json.loads(response["body"])
            assert body["processed_events"] == 1
            assert body["duplicate_events"] == 0
            assert body["failed_events"] == 0

    def test_handler_with_duplicates(self):
        """Test handler with duplicate events."""
        event = {"Records": [{"messageId": "msg-1", "body": "<LogEvent></LogEvent>"}]}
        context = MockLambdaContext()

        mock_result = {
            "total_events": 1,
            "processed_events": 0,
            "duplicate_events": 1,
            "failed_events": 0,
            "event_hashes": [],
            "processing_time_ms": 50.0,
        }

        with patch(
            "src.lambda_function._process_events_async", new_callable=AsyncMock
        ) as mock_process:
            mock_process.return_value = mock_result

            response = lambda_handler(event, context)

            assert response["statusCode"] == 200
            body = json.loads(response["body"])
            assert body["duplicate_events"] == 1

    def test_handler_with_failures(self):
        """Test handler with failed events."""
        event = {"Records": [{"messageId": "msg-1", "body": "<LogEvent></LogEvent>"}]}
        context = MockLambdaContext()

        mock_result = {
            "total_events": 1,
            "processed_events": 0,
            "duplicate_events": 0,
            "failed_events": 1,
            "event_hashes": [],
            "processing_time_ms": 75.0,
        }

        with patch(
            "src.lambda_function._process_events_async", new_callable=AsyncMock
        ) as mock_process:
            mock_process.return_value = mock_result

            response = lambda_handler(event, context)

            assert response["statusCode"] == 200
            body = json.loads(response["body"])
            assert body["failed_events"] == 1

    def test_handler_with_sns_wrapped_message(self):
        """Test handler with SNS-wrapped SQS message."""
        sns_message = {"Message": "<LogEvent><eventType>Test</eventType></LogEvent>"}
        event = {
            "Records": [
                {
                    "messageId": "msg-1",
                    "eventSource": "aws:sns",
                    "body": json.dumps(sns_message),
                }
            ]
        }
        context = MockLambdaContext()

        mock_result = {
            "total_events": 1,
            "processed_events": 1,
            "duplicate_events": 0,
            "failed_events": 0,
            "event_hashes": ["abc123"],
            "processing_time_ms": 100.0,
        }

        with patch(
            "src.lambda_function._process_events_async", new_callable=AsyncMock
        ) as mock_process:
            mock_process.return_value = mock_result

            response = lambda_handler(event, context)

            assert response["statusCode"] == 200
            # Verify SNS message was unwrapped
            mock_process.assert_called_once()
            call_args = mock_process.call_args[0]
            assert call_args[0][0] == sns_message["Message"]

    def test_handler_with_exception(self):
        """Test handler with exception during processing."""
        event = {"Records": [{"messageId": "msg-1", "body": "<LogEvent></LogEvent>"}]}
        context = MockLambdaContext()

        with patch(
            "src.lambda_function._process_events_async", new_callable=AsyncMock
        ) as mock_process:
            mock_process.side_effect = Exception("Test error")

            with pytest.raises(Exception, match="Test error"):
                lambda_handler(event, context)

    def test_handler_with_multiple_records(self):
        """Test handler with multiple SQS records."""
        event = {
            "Records": [
                {
                    "messageId": "msg-1",
                    "body": "<LogEvent><eventType>Test1</eventType></LogEvent>",
                },
                {
                    "messageId": "msg-2",
                    "body": "<LogEvent><eventType>Test2</eventType></LogEvent>",
                },
                {
                    "messageId": "msg-3",
                    "body": "<LogEvent><eventType>Test3</eventType></LogEvent>",
                },
            ]
        }
        context = MockLambdaContext()

        mock_result = {
            "total_events": 3,
            "processed_events": 3,
            "duplicate_events": 0,
            "failed_events": 0,
            "event_hashes": ["abc123", "def456", "ghi789"],
            "batch_id": "batch-123",
            "processing_time_ms": 300.0,
        }

        with patch(
            "src.lambda_function._process_events_async", new_callable=AsyncMock
        ) as mock_process:
            mock_process.return_value = mock_result

            response = lambda_handler(event, context)
            print(response)

            assert response["statusCode"] == 200
            body = json.loads(response["body"])
            assert body["total_events"] == 3
            assert body["processed_events"] == 3
            # Verify all message IDs were extracted
            call_args = mock_process.call_args[0]
            assert len(call_args[1]) == 3  # sqs_message_ids list
