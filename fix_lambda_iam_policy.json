{"Version": "2012-10-17", "Statement": [{"Action": "secretsmanager:GetSecretValue", "Effect": "Allow", "Resource": "arn:aws:secretsmanager:ap-southeast-2:024660257967:secret:dev-au-smartanalytics-common-aurora-credentials-for-db-proxy*", "Sid": "SecretRead"}, {"Action": ["s3:GetObjectTagging", "s3:GetObject"], "Effect": "Allow", "Resource": "arn:aws:s3:::dev-au-smartanalytics-au-sa-ambulance-lambdas/acd-event-processor.zip", "Sid": "S3LambdaCodeAccess"}, {"Action": ["kms:GenerateDataKeyWithoutPlaintext", "kms:GenerateDataKey", "kms:Decrypt"], "Effect": "Allow", "Resource": ["arn:aws:kms:ap-southeast-2:024660257967:key/dev-au-smartanalytics-au-sa-ambulance-key", "arn:aws:kms:ap-southeast-2:024660257967:alias/dev-au-smartanalytics-au-sa-ambulance-key"], "Sid": "KMSDecryptForReads"}, {"Action": "rds-db:connect", "Effect": "Allow", "Resource": "arn:aws:rds-db:ap-southeast-2:024660257967:dbuser:prx-003e87a11a1e672f2/Allerium", "Sid": "RDSConnect"}, {"Action": ["rds:DescribeDBProxyTargets", "rds:DescribeDBProxies", "rds:DescribeDBInstances", "rds:DescribeDBClusters"], "Effect": "Allow", "Resource": ["arn:aws:rds:ap-southeast-2:024660257967:db:*", "arn:aws:rds:ap-southeast-2:024660257967:db-proxy:*", "arn:aws:rds:ap-southeast-2:024660257967:cluster:dev-au-smartanalytics-common-aurora"], "Sid": "RDSDescribe"}, {"Action": ["ec2:UnassignPrivateIpAddresses", "ec2:DescribeSubnets", "ec2:DescribeNetworkInterfaces", "ec2:DeleteNetworkInterface", "ec2:CreateNetworkInterface", "ec2:AssignPrivateIpAddresses"], "Effect": "Allow", "Resource": ["arn:aws:ec2:ap-southeast-2:024660257967:vpc/vpc-0f6207bd1ffbd72ac", "arn:aws:ec2:ap-southeast-2:024660257967:subnet/*", "arn:aws:ec2:ap-southeast-2:024660257967:security-group/*", "arn:aws:ec2:ap-southeast-2:024660257967:network-interface/*"], "Sid": "VPCAccess"}, {"Action": "logs:CreateLogGroup", "Effect": "Allow", "Resource": "*", "Sid": "LogsCreateGroup"}, {"Action": ["logs:PutLogEvents", "logs:CreateLogStream"], "Effect": "Allow", "Resource": "arn:aws:logs:ap-southeast-2:024660257967:log-group:/aws/lambda/dev-au-smartanalytics-au-sa-ambulance-acd-processor:*", "Sid": "LogsWrite"}]}