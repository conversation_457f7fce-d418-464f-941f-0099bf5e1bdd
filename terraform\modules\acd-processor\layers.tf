# Lambda Layers for ACD Event Processor

# Upload utilities layer zip to S3
resource "aws_s3_object" "utilities_layer" {
  bucket                 = var.s3_lambda_code_bucket_name
  key                    = "layers/utilities-layer.zip"
  source                 = "${var.project_dir}/build/utilities-layer.zip"
  checksum_algorithm     = "SHA256"
  server_side_encryption = "aws:kms"
  kms_key_id             = "arn:${data.aws_partition.current.partition}:kms:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:alias/${var.kms_key_name}"

  tags = merge(
    var.tags,
    {
      Name  = "${var.name_prefix}-utilities-layer"
    }
  )
}

# Upload domain layer zip to S3
resource "aws_s3_object" "domain_layer" {
  bucket                 = var.s3_lambda_code_bucket_name
  key                    = "layers/domain-layer.zip"
  source                 = "${var.project_dir}/build/domain-layer.zip"
  checksum_algorithm     = "SHA256"
  server_side_encryption = "aws:kms"
  kms_key_id             = "arn:${data.aws_partition.current.partition}:kms:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:alias/${var.kms_key_name}"

  tags = merge(
    var.tags,
    {
      Name  = "${var.name_prefix}-domain-layer"
    }
  )
}

# Upload infrastructure layer zip to S3
resource "aws_s3_object" "infrastructure_layer" {
  bucket                 = var.s3_lambda_code_bucket_name
  key                    = "layers/infrastructure-layer.zip"
  source                 = "${var.project_dir}/build/infrastructure-layer.zip"
  checksum_algorithm     = "SHA256"
  server_side_encryption = "aws:kms"
  kms_key_id             = "arn:${data.aws_partition.current.partition}:kms:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:alias/${var.kms_key_name}"

  tags = merge(
    var.tags,
    {
      Name  = "${var.name_prefix}-infrastructure-layer"
    }
  )
}

# Lambda Layer Versions
# Utilities layer - provides shared utilities (config, logging, datetime, hash, XML)
module "utilities_layer" {
  source = "../lambda-layer"

  layer_name       = "${var.name_prefix}-utilities"
  description      = "SmartAnalytics utilities layer with pydantic, aws-lambda-powertools, xmltodict, and helpers"
  s3_bucket        = aws_s3_object.utilities_layer.bucket
  s3_key           = aws_s3_object.utilities_layer.key
  source_code_hash = filebase64sha256("${var.project_dir}/build/utilities-layer.zip")

  compatible_runtimes      = ["python3.12"]
  compatible_architectures = ["x86_64"]
}

# Domain layer - provides business logic and domain models
module "domain_layer" {
  source = "../lambda-layer"

  layer_name       = "${var.name_prefix}-domain"
  description      = "SmartAnalytics domain layer with business logic, commands, and handlers"
  s3_bucket        = aws_s3_object.domain_layer.bucket
  s3_key           = aws_s3_object.domain_layer.key
  source_code_hash = filebase64sha256("${var.project_dir}/build/domain-layer.zip")

  compatible_runtimes      = ["python3.12"]
  compatible_architectures = ["x86_64"]
}

# Infrastructure layer - provides database connectivity and repositories
module "infrastructure_layer" {
  source = "../lambda-layer"

  layer_name       = "${var.name_prefix}-infrastructure"
  description      = "SmartAnalytics infrastructure layer with SQLAlchemy, asyncpg, and repositories"
  s3_bucket        = aws_s3_object.infrastructure_layer.bucket
  s3_key           = aws_s3_object.infrastructure_layer.key
  source_code_hash = filebase64sha256("${var.project_dir}/build/infrastructure-layer.zip")

  compatible_runtimes      = ["python3.12"]
  compatible_architectures = ["x86_64"]
}
