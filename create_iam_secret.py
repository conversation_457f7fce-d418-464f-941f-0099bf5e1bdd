#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create the IAM authentication secret for RDS Proxy.

This script creates a new AWS Secrets Manager secret specifically for
IAM authentication with the common_user database user.
"""

import boto3
import json
import sys
from botocore.exceptions import ClientError


def create_iam_secret():
    """Create the IAM authentication secret."""
    
    # Configuration
    secret_name = "dev-au-smartanalytics-common-aurora-credentials-iam"
    region = "ap-southeast-2"
    
    # Secret content - username must match the database user for IAM auth
    secret_value = {
        "username": "common_user",
        "password": "not-used-for-iam-auth"  # Required by format but not used
    }
    
    # Create Secrets Manager client
    client = boto3.client('secretsmanager', region_name=region)
    
    try:
        # Check if secret already exists
        try:
            response = client.describe_secret(SecretId=secret_name)
            print(f"✅ Secret '{secret_name}' already exists")
            print(f"   ARN: {response['ARN']}")
            
            # Update the secret value
            client.update_secret(
                SecretId=secret_name,
                SecretString=json.dumps(secret_value)
            )
            print(f"✅ Secret value updated successfully")
            return response['ARN']
            
        except ClientError as e:
            if e.response['Error']['Code'] == 'ResourceNotFoundException':
                # Secret doesn't exist, create it
                print(f"🔄 Creating new secret '{secret_name}'...")
                
                response = client.create_secret(
                    Name=secret_name,
                    Description="Aurora database credentials for IAM authentication via RDS Proxy",
                    SecretString=json.dumps(secret_value),
                    Tags=[
                        {
                            'Key': 'Name',
                            'Value': secret_name
                        },
                        {
                            'Key': 'Purpose',
                            'Value': 'RDS-Proxy-IAM-Auth'
                        }
                    ]
                )
                
                print(f"✅ Secret created successfully!")
                print(f"   ARN: {response['ARN']}")
                return response['ARN']
            else:
                raise
                
    except ClientError as e:
        print(f"❌ Error creating secret: {e}")
        return None
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return None


def verify_secret(secret_arn):
    """Verify the secret was created correctly."""
    
    region = "ap-southeast-2"
    client = boto3.client('secretsmanager', region_name=region)
    
    try:
        response = client.get_secret_value(SecretId=secret_arn)
        secret_data = json.loads(response['SecretString'])
        
        print(f"\n🔍 Secret verification:")
        print(f"   Username: {secret_data.get('username')}")
        print(f"   Password: {'***' if secret_data.get('password') else 'Not set'}")
        
        if secret_data.get('username') == 'common_user':
            print(f"✅ Secret contains correct username for IAM auth")
            return True
        else:
            print(f"❌ Secret contains wrong username: {secret_data.get('username')}")
            return False
            
    except Exception as e:
        print(f"❌ Error verifying secret: {e}")
        return False


def main():
    """Main function."""
    
    print("🚀 Creating IAM Authentication Secret for RDS Proxy")
    print("=" * 60)
    
    # Create the secret
    secret_arn = create_iam_secret()
    
    if secret_arn:
        # Verify the secret
        if verify_secret(secret_arn):
            print(f"\n✅ Secret setup complete!")
            print(f"\nNext steps:")
            print(f"1. Update your Terraform configuration to use this secret")
            print(f"2. Apply the Terraform changes")
            print(f"3. Test IAM authentication with common_user")
            print(f"\nSecret ARN to use in Terraform:")
            print(f"   {secret_arn}")
        else:
            print(f"\n❌ Secret verification failed")
            sys.exit(1)
    else:
        print(f"\n❌ Failed to create secret")
        sys.exit(1)


if __name__ == "__main__":
    main()
