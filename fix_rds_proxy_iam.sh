#!/bin/bash

# RDS Proxy IAM Authentication Fix Script
# This script fixes the RDS Proxy configuration to use the correct secret for IAM auth

set -e

# Configuration
PROXY_NAME="dev-au-smartanalytics-common-aurora-db-proxy"
REGION="ap-southeast-2"
IAM_SECRET_ARN="arn:aws:secretsmanager:ap-southeast-2:024660257967:secret:dev-au-smartanalytics-common-aurora-credentials-for-db-proxy-f3jyke"
PASSWORD_SECRET_ARN="arn:aws:secretsmanager:ap-southeast-2:024660257967:secret:dev-au-smartanalytics-common-aurora-credentials-87Gb1f"

echo "🚀 Fixing RDS Proxy IAM Authentication"
echo "======================================"

# Step 1: Get current proxy configuration
echo "📋 Getting current proxy configuration..."
aws rds describe-db-proxies \
    --db-proxy-name "$PROXY_NAME" \
    --region "$REGION" \
    --query 'DBProxies[0].Auth' \
    --output table

# Step 2: Verify the IAM secret contains common_user
echo ""
echo "🔍 Verifying IAM secret contains common_user..."
SECRET_VALUE=$(aws secretsmanager get-secret-value \
    --secret-id "$IAM_SECRET_ARN" \
    --region "$REGION" \
    --query 'SecretString' \
    --output text)

USERNAME=$(echo "$SECRET_VALUE" | jq -r '.username')
echo "   Secret username: $USERNAME"

if [ "$USERNAME" != "common_user" ]; then
    echo "❌ ERROR: Secret does not contain common_user username!"
    echo "   Current username: $USERNAME"
    echo "   Expected username: common_user"
    exit 1
fi

echo "✅ Secret contains correct username: $USERNAME"

# Step 3: Update RDS Proxy auth configuration
echo ""
echo "🔧 Updating RDS Proxy authentication configuration..."

# Create the auth configuration JSON
AUTH_CONFIG='[
    {
        "Description": "RDS Proxy Password Auth",
        "AuthScheme": "SECRETS",
        "SecretArn": "'$PASSWORD_SECRET_ARN'",
        "IAMAuth": "DISABLED",
        "ClientPasswordAuthType": "POSTGRES_SCRAM_SHA_256"
    },
    {
        "Description": "RDS Proxy IAM Auth",
        "AuthScheme": "SECRETS", 
        "SecretArn": "'$IAM_SECRET_ARN'",
        "IAMAuth": "REQUIRED",
        "ClientPasswordAuthType": "POSTGRES_SCRAM_SHA_256"
    }
]'

echo "   Applying new auth configuration..."

# Update the proxy
aws rds modify-db-proxy \
    --db-proxy-name "$PROXY_NAME" \
    --auth "$AUTH_CONFIG" \
    --region "$REGION" \
    --output table

echo "✅ RDS Proxy configuration updated successfully!"

# Step 4: Wait for the update to complete
echo ""
echo "⏳ Waiting for proxy update to complete..."
echo "   This may take a few minutes..."

# Poll the proxy status until it's available
while true; do
    STATUS=$(aws rds describe-db-proxies \
        --db-proxy-name "$PROXY_NAME" \
        --region "$REGION" \
        --query 'DBProxies[0].Status' \
        --output text)
    
    echo "   Current status: $STATUS"
    
    if [ "$STATUS" = "available" ]; then
        echo "✅ Proxy is now available!"
        break
    elif [ "$STATUS" = "failed" ]; then
        echo "❌ Proxy update failed!"
        exit 1
    fi
    
    sleep 30
done

# Step 5: Verify the final configuration
echo ""
echo "🔍 Verifying final proxy configuration..."
aws rds describe-db-proxies \
    --db-proxy-name "$PROXY_NAME" \
    --region "$REGION" \
    --query 'DBProxies[0].Auth' \
    --output table

echo ""
echo "✅ RDS Proxy IAM authentication fix completed!"
echo ""
echo "📝 Summary of changes:"
echo "   - Password Auth: Uses secret with 'Allerium' user (IAM disabled)"
echo "   - IAM Auth: Uses secret with 'common_user' user (IAM required)"
echo ""
echo "🧪 Test your IAM authentication now with:"
echo "   python test_iam_token.py"
