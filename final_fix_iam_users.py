#!/usr/bin/env python3
"""Final fix: Grant rds_iam role to both master and common users."""

import asyncio
import sys

import asyncpg


async def fix_iam_users():
    """Grant rds_iam role to both users."""
    print("🔧 Final fix: Granting rds_iam role to both users...")
    
    # Connect directly to Aurora primary-1 using master credentials
    host = "dev-au-smartanalytics-common-aurora-primary-1.c3atdbtyiqrw.ap-southeast-2.rds.amazonaws.com"
    port = 5432
    database = "prod"
    username = "Allerium"
    password = "72bOkOq23DL3MuUa"
    
    print(f"   Host: {host}")
    print(f"   Database: {database}")
    print(f"   Master user: {username}")
    
    try:
        # Connect using master credentials
        print("   Connecting with master credentials...")
        conn = await asyncpg.connect(
            host=host,
            port=port,
            database=database,
            user=username,
            password=password,
            ssl="require"
        )
        
        print("✅ Connected successfully!")
        
        # Grant rds_iam role to master user (Allerium)
        print(f"   Granting rds_iam role to master user: {username}")
        try:
            await conn.execute(f'GRANT rds_iam TO "{username}"')
            print(f"✅ Granted rds_iam role to {username}")
        except Exception as e:
            if "already a member" in str(e).lower():
                print(f"✅ {username} already has rds_iam role")
            else:
                print(f"⚠️  Warning granting to {username}: {e}")
        
        # Grant rds_iam role to common_user
        print("   Granting rds_iam role to common_user")
        try:
            await conn.execute('GRANT rds_iam TO common_user')
            print("✅ Granted rds_iam role to common_user")
        except Exception as e:
            if "already a member" in str(e).lower():
                print("✅ common_user already has rds_iam role")
            else:
                print(f"⚠️  Warning granting to common_user: {e}")
        
        # Verify both users have rds_iam role
        print("\n   Verifying IAM roles...")
        
        # Check master user
        has_iam_master = await conn.fetchval(
            "SELECT 1 FROM pg_auth_members m "
            "JOIN pg_roles r ON m.roleid = r.oid "
            "JOIN pg_roles u ON m.member = u.oid "
            "WHERE r.rolname = 'rds_iam' AND u.rolname = $1",
            username
        )
        
        # Check common_user
        has_iam_common = await conn.fetchval(
            "SELECT 1 FROM pg_auth_members m "
            "JOIN pg_roles r ON m.roleid = r.oid "
            "JOIN pg_roles u ON m.member = u.oid "
            "WHERE r.rolname = 'rds_iam' AND u.rolname = $1",
            "common_user"
        )
        
        print(f"   Master user ({username}) has rds_iam: {'✅ Yes' if has_iam_master else '❌ No'}")
        print(f"   common_user has rds_iam: {'✅ Yes' if has_iam_common else '❌ No'}")
        
        await conn.close()
        
        if has_iam_master and has_iam_common:
            print("\n✅ Both users now have rds_iam role!")
            return True
        else:
            print("\n❌ Failed to grant rds_iam role to all users")
            return False
        
    except Exception as e:
        print(f"❌ Failed to fix IAM users: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Main function."""
    print("🚀 Final Fix: IAM Database Users")
    print("=" * 50)
    
    success = await fix_iam_users()
    
    if success:
        print("\n🎉 READY TO TEST!")
        print("   Both users now have rds_iam role.")
        print("   RDS Proxy IAM authentication should work!")
        return 0
    else:
        print("\n❌ Fix failed.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
