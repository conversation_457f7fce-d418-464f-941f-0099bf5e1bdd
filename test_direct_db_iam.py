#!/usr/bin/env python3
"""Test IAM token authentication directly to database (bypassing proxy)."""

import asyncio
import boto3
import asyncpg
from dotenv import load_dotenv

load_dotenv()


async def test_direct_db_connection():
    """Test direct connection to database with IAM token."""
    print("🔑 Testing direct database IAM authentication...")
    
    # Configuration for direct database connection
    host = "dev-au-smartanalytics-common-aurora.cluster-c3atdbtyiqrw.ap-southeast-2.rds.amazonaws.com"  # Direct cluster endpoint
    port = 5432
    database = "prod"
    username = "common_user"
    region = "ap-southeast-2"
    
    print(f"   Host: {host}")
    print(f"   Port: {port}")
    print(f"   Database: {database}")
    print(f"   Username: {username}")
    print(f"   Region: {region}")
    
    try:
        # Generate IAM token for direct database connection
        print("   Generating IAM token for direct database...")
        rds_client = boto3.client("rds", region_name=region)
        
        token = rds_client.generate_db_auth_token(
            DBHostname=host,
            Port=port,
            DBUsername=username,
            Region=region
        )
        
        print(f"✅ IAM token generated successfully!")
        print(f"   Token length: {len(token)} characters")
        
        # Test connection with IAM token
        print("   Testing direct database connection with IAM token...")
        
        conn = await asyncpg.connect(
            host=host,
            port=port,
            database=database,
            user=username,
            password=token,
            ssl="require"
        )
        
        print("✅ Direct database IAM connection successful!")
        
        # Test basic query
        result = await conn.fetchval("SELECT 1")
        print(f"   Test query result: {result}")
        
        # Test current user
        current_user = await conn.fetchval("SELECT current_user")
        print(f"   Connected as: {current_user}")
        
        # Check if user has rds_iam role
        has_iam_role = await conn.fetchval("""
            SELECT EXISTS(
                SELECT 1 FROM pg_roles r 
                JOIN pg_auth_members m ON r.oid = m.roleid 
                JOIN pg_roles u ON m.member = u.oid 
                WHERE u.rolname = 'common_user' AND r.rolname = 'rds_iam'
            )
        """)
        print(f"   Has rds_iam role: {has_iam_role}")
        
        # Test schema access
        schemas = await conn.fetch("SELECT schema_name FROM information_schema.schemata WHERE schema_name IN ('common', 'memo', 'public')")
        print(f"   Available schemas: {[row['schema_name'] for row in schemas]}")
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Direct database IAM connection failed: {e}")
        print(f"   Error type: {type(e).__name__}")
        return False


async def test_proxy_connection():
    """Test connection through proxy with IAM token."""
    print("\n🔑 Testing proxy IAM authentication...")
    
    # Configuration for proxy connection
    host = "dev-au-smartanalytics-common-aurora-db-proxy.proxy-c3atdbtyiqrw.ap-southeast-2.rds.amazonaws.com"
    port = 5432
    database = "prod"
    username = "common_user"
    region = "ap-southeast-2"
    
    print(f"   Host: {host}")
    print(f"   Port: {port}")
    print(f"   Database: {database}")
    print(f"   Username: {username}")
    print(f"   Region: {region}")
    
    try:
        # Generate IAM token for proxy connection
        print("   Generating IAM token for proxy...")
        rds_client = boto3.client("rds", region_name=region)
        
        token = rds_client.generate_db_auth_token(
            DBHostname=host,
            Port=port,
            DBUsername=username,
            Region=region
        )
        
        print(f"✅ IAM token generated successfully!")
        print(f"   Token length: {len(token)} characters")
        
        # Test connection with IAM token
        print("   Testing proxy connection with IAM token...")
        
        conn = await asyncpg.connect(
            host=host,
            port=port,
            database=database,
            user=username,
            password=token,
            ssl="require"
        )
        
        print("✅ Proxy IAM connection successful!")
        
        # Test basic query
        result = await conn.fetchval("SELECT 1")
        print(f"   Test query result: {result}")
        
        # Test current user
        current_user = await conn.fetchval("SELECT current_user")
        print(f"   Connected as: {current_user}")
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Proxy IAM connection failed: {e}")
        print(f"   Error type: {type(e).__name__}")
        return False


async def main():
    """Main test function."""
    print("🚀 Testing IAM Authentication - Direct vs Proxy")
    print("=" * 60)
    
    # Test direct database connection first
    direct_success = await test_direct_db_connection()
    
    # Test proxy connection
    proxy_success = await test_proxy_connection()
    
    print("\n" + "=" * 60)
    print("📊 Results Summary:")
    print(f"   Direct Database IAM: {'✅ Success' if direct_success else '❌ Failed'}")
    print(f"   Proxy IAM: {'✅ Success' if proxy_success else '❌ Failed'}")
    
    if direct_success and not proxy_success:
        print("\n🔍 Analysis: Database user is set up correctly, but proxy configuration has issues.")
        print("   Next steps:")
        print("   1. Check RDS Proxy auth configuration")
        print("   2. Verify proxy has access to the correct secret")
        print("   3. Check proxy security groups and networking")
    elif not direct_success:
        print("\n🔍 Analysis: Database user setup has issues.")
        print("   Next steps:")
        print("   1. Verify common_user exists in database")
        print("   2. Check if common_user has rds_iam role")
        print("   3. Verify IAM permissions")
    elif direct_success and proxy_success:
        print("\n🎉 Both connections work! IAM authentication is properly configured.")
    
    return 0 if (direct_success and proxy_success) else 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
