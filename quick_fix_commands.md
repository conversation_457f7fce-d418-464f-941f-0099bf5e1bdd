# Quick Fix Commands for RDS Proxy IAM Authentication

## Problem
Your RDS Proxy has two auth configurations, but both are pointing to secrets with different usernames than what you're trying to connect with.

## Current Status
- You updated the secret `dev-au-smartanalytics-common-aurora-credentials-for-db-proxy-f3jyke` to contain `username: common_user`
- But the RDS Proxy configuration hasn't been updated to reflect this change

## Fix Commands

### 1. First, let's verify your current proxy configuration:

```bash
aws rds describe-db-proxies \
    --db-proxy-name "dev-au-smartanalytics-common-aurora-db-proxy" \
    --region "ap-southeast-2" \
    --query 'DBProxies[0].Auth' \
    --output table
```

### 2. Verify the secret contains common_user:

```bash
aws secretsmanager get-secret-value \
    --secret-id "arn:aws:secretsmanager:ap-southeast-2:024660257967:secret:dev-au-smartanalytics-common-aurora-credentials-for-db-proxy-f3jyke" \
    --region "ap-southeast-2" \
    --query 'SecretString' \
    --output text | jq '.'
```

### 3. Update the RDS Proxy auth configuration:

```bash
aws rds modify-db-proxy \
    --db-proxy-name "dev-au-smartanalytics-common-aurora-db-proxy" \
    --region "ap-southeast-2" \
    --auth '[
        {
            "Description": "RDS Proxy Password Auth",
            "AuthScheme": "SECRETS",
            "SecretArn": "arn:aws:secretsmanager:ap-southeast-2:024660257967:secret:dev-au-smartanalytics-common-aurora-credentials-87Gb1f",
            "IAMAuth": "DISABLED",
            "ClientPasswordAuthType": "POSTGRES_SCRAM_SHA_256"
        },
        {
            "Description": "RDS Proxy IAM Auth", 
            "AuthScheme": "SECRETS",
            "SecretArn": "arn:aws:secretsmanager:ap-southeast-2:024660257967:secret:dev-au-smartanalytics-common-aurora-credentials-for-db-proxy-f3jyke",
            "IAMAuth": "REQUIRED",
            "ClientPasswordAuthType": "POSTGRES_SCRAM_SHA_256"
        }
    ]'
```

### 4. Wait for the update to complete (check status):

```bash
aws rds describe-db-proxies \
    --db-proxy-name "dev-au-smartanalytics-common-aurora-db-proxy" \
    --region "ap-southeast-2" \
    --query 'DBProxies[0].Status' \
    --output text
```

Keep running this until it shows `available` instead of `modifying`.

### 5. Verify the final configuration:

```bash
aws rds describe-db-proxies \
    --db-proxy-name "dev-au-smartanalytics-common-aurora-db-proxy" \
    --region "ap-southeast-2" \
    --query 'DBProxies[0].Auth' \
    --output table
```

### 6. Test your IAM authentication:

```bash
python test_iam_token.py
```

## What This Fix Does

1. **Password Auth (First auth block)**: 
   - Uses secret with `username: Allerium` 
   - IAM authentication is DISABLED
   - For regular username/password connections

2. **IAM Auth (Second auth block)**:
   - Uses secret with `username: common_user`
   - IAM authentication is REQUIRED  
   - For IAM token-based connections

## Expected Result

After this fix:
- ✅ Password authentication will work with `Allerium` user
- ✅ IAM authentication will work with `common_user` 
- ✅ Both will work through the RDS Proxy

The key insight is that RDS Proxy uses the username from the secret to determine which database user to authenticate as when using IAM tokens.
