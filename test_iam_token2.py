#!/usr/bin/env python3
import sys, asyncio, boto3, psycopg
from psycopg.rows import tuple_row

# --- Windows-only fix: use Selector loop ---
if sys.platform.startswith("win"):
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
HOST   = "dev-au-smartanalytics-common-aurora.cluster-c3atdbtyiqrw.ap-southeast-2.rds.amazonaws.com"

#HOST   = "dev-au-smartanalytics-common-aurora-db-proxy.proxy-c3atdbtyiqrw.ap-southeast-2.rds.amazonaws.com"
DB     = "prod"
USER   = "common_user"
REGION = "ap-southeast-2"

async def main():
    rds = boto3.client("rds", region_name=REGION)
    token = rds.generate_db_auth_token(DBHostname=HOST, Port=5432, DBUsername=USER, Region=REGION)

    async with await psycopg.AsyncConnection.connect(
        host=HOST,
        port=5432,
        dbname=DB,
        user=USER,
        password=token,
        sslmode="require",
        prepare_threshold=0

    ) as conn:
        async with conn.cursor(row_factory=tuple_row) as cur:
            await cur.execute("select current_user, current_database()")
            print(await cur.fetchone())

if __name__ == "__main__":
    asyncio.run(main())
