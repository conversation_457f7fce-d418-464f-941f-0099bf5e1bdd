#!/usr/bin/env python3
"""Test RDS Proxy with password authentication."""

import asyncio
import sys

import asyncpg


async def test_proxy_password():
    """Test connection to RDS Proxy with password auth."""
    print("🔧 Testing RDS Proxy with password authentication...")
    
    host = "dev-au-smartanalytics-common-aurora-db-proxy.proxy-c3atdbtyiqrw.ap-southeast-2.rds.amazonaws.com"
    port = 5432
    database = "prod"
    username = "Allerium"  # Master user
    password = "72bOkOq23DL3MuUa"  # Master password
    
    print(f"   Host: {host}")
    print(f"   Database: {database}")
    print(f"   Username: {username} (password auth)")
    
    try:
        # Connect using password
        print("   Connecting with password...")
        conn = await asyncpg.connect(
            host=host,
            port=port,
            database=database,
            user=username,
            password=password,
            ssl="require",
            command_timeout=30
        )
        
        print("✅ Connected successfully!")
        
        # Simple test query
        result = await conn.fetchval("SELECT 1")
        print(f"✅ Query successful! Result: {result}")
        
        # Test schema access
        tables = await conn.fetch(
            "SELECT table_name FROM information_schema.tables WHERE table_schema = $1",
            "common"
        )
        print(f"✅ Schema access successful! Found {len(tables)} tables")
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Proxy password authentication failed: {e}")
        print(f"   Error type: {type(e).__name__}")
        return False


async def main():
    """Main function."""
    print("🚀 Testing RDS Proxy Password Authentication")
    print("=" * 60)
    
    success = await test_proxy_password()
    
    if success:
        print("\n✅ RDS Proxy password authentication works!")
        print("   The proxy is working, issue is with IAM auth.")
        return 0
    else:
        print("\n❌ RDS Proxy password authentication failed.")
        print("   The proxy itself has issues.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
