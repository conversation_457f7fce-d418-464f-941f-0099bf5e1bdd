#!/usr/bin/env python3
"""Test IAM token authentication."""

import asyncio
import os
import sys

import asyncpg
import boto3
from dotenv import load_dotenv

load_dotenv()


async def test_iam_token_connection():
    """Test connection with IAM token."""
    print("🔑 Testing IAM token authentication...")
    
    # Configuration
    host = "dev-au-smartanalytics-common-aurora-db-proxy.proxy-c3atdbtyiqrw.ap-southeast-2.rds.amazonaws.com"
    port = 5432
    database = "prod"
    username = "common_user"
    region = "ap-southeast-2"
    
    print(f"   Host: {host}")
    print(f"   Port: {port}")
    print(f"   Database: {database}")
    print(f"   Username: {username}")
    print(f"   Region: {region}")
    
    try:
        # Generate IAM token
        print("   Generating IAM token...")
        rds_client = boto3.client("rds", region_name=region)
        
        token = rds_client.generate_db_auth_token(
            DBHostname=host,
            Port=port,
            DBUsername=username,
            Region=region
        )
        
        print(f"✅ IAM token generated successfully!")
        print(f"   Token length: {len(token)} characters")
        print(f"   Token preview: {token[:50]}...")
        
        # Test connection with IAM token
        print("   Testing connection with IAM token...")
        
        conn = await asyncpg.connect(
            host=host,
            port=port,
            database=database,
            user=username,
            password=token,
            ssl="require"
        )
        
        print("✅ IAM token connection successful!")
        
        # Test basic query
        result = await conn.fetchval("SELECT 1")
        print(f"   Test query result: {result}")
        
        # Test current user
        current_user = await conn.fetchval("SELECT current_user")
        print(f"   Connected as: {current_user}")
        
        # Test schema access
        try:
            schema_test = await conn.fetchval("SELECT 1 FROM information_schema.schemata WHERE schema_name = 'common'")
            print(f"   Can access common schema: {schema_test is not None}")
        except Exception as e:
            print(f"   Schema access test failed: {e}")
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f"❌ IAM token connection failed: {e}")
        print(f"   Error type: {type(e).__name__}")
        
        # Check if it's an authentication error
        if "authentication" in str(e).lower() or "credentials" in str(e).lower():
            print("   This appears to be an authentication error.")
            print("   Possible causes:")
            print("   1. IAM permissions issue")
            print("   2. RDS Proxy configuration issue")
            print("   3. Database user setup issue")
        
        return False


async def test_lambda_iam_permissions():
    """Test if current AWS credentials have the right permissions."""
    print("\n🔐 Testing AWS IAM permissions...")
    
    try:
        # Test STS identity
        sts_client = boto3.client("sts")
        identity = sts_client.get_caller_identity()
        print(f"   AWS Identity: {identity.get('Arn', 'Unknown')}")
        
        # Test RDS permissions
        rds_client = boto3.client("rds", region_name="ap-southeast-2")
        
        # Test describe permissions
        try:
            proxies = rds_client.describe_db_proxies()
            print(f"   Can describe RDS proxies: ✅ ({len(proxies['DBProxies'])} found)")
        except Exception as e:
            print(f"   Cannot describe RDS proxies: ❌ {e}")
        
        # Test token generation
        try:
            token = rds_client.generate_db_auth_token(
                DBHostname="dev-au-smartanalytics-common-aurora-db-proxy.proxy-c3atdbtyiqrw.ap-southeast-2.rds.amazonaws.com",
                Port=5432,
                DBUsername="common_user",
                Region="ap-southeast-2"
            )
            print(f"   Can generate IAM tokens: ✅")
        except Exception as e:
            print(f"   Cannot generate IAM tokens: ❌ {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ AWS permissions test failed: {e}")
        return False


async def main():
    """Main test function."""
    print("🚀 Testing IAM Token Authentication")
    print("=" * 50)
    
    # Test AWS permissions first
    perms_success = await test_lambda_iam_permissions()
    
    if perms_success:
        # Test IAM token connection
        token_success = await test_iam_token_connection()
        
        if token_success:
            print("\n✅ IAM token authentication works! Your Lambda should work now.")
            return 0
        else:
            print("\n❌ IAM token authentication failed.")
            print("   Check the error details above for troubleshooting.")
    else:
        print("\n❌ AWS permissions test failed.")
        print("   Make sure you have the right AWS credentials configured.")
    
    return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
