#!/usr/bin/env python3
"""Create common_user directly on Aurora instance."""

import asyncio
import sys

import asyncpg


async def create_common_user():
    """Create the common_user directly on Aurora."""
    print("🔧 Creating common_user directly on Aurora instance...")
    
    # Master user credentials
    master_username = "Allerium"
    master_password = "72bOkOq23DL3MuUa"
    
    # Connect directly to primary instance (primary-1 should be the writer)
    host = "dev-au-smartanalytics-common-aurora-primary-1.c3atdbtyiqrw.ap-southeast-2.rds.amazonaws.com"
    port = 5432
    database = "prod"
    
    print(f"   Host: {host}")
    print(f"   Database: {database}")
    print(f"   Master User: {master_username}")
    
    try:
        conn = await asyncpg.connect(
            host=host,
            port=port,
            database=database,
            user=master_username,
            password=master_password,
            ssl="require"
        )
        
        print("✅ Connected as master user")
        
        # Check if common_user exists
        user_exists = await conn.fetchval(
            "SELECT EXISTS(SELECT 1 FROM pg_user WHERE usename = $1)",
            "common_user"
        )
        
        if user_exists:
            print("   common_user already exists, checking configuration...")
        else:
            # Create the user
            print("   Creating common_user...")
            await conn.execute("CREATE USER common_user")
        
        # Grant rds_iam role
        print("   Granting rds_iam role...")
        await conn.execute("GRANT rds_iam TO common_user")
        
        # Create common schema if it doesn't exist
        await conn.execute('CREATE SCHEMA IF NOT EXISTS "common"')
        
        # Grant schema permissions
        print("   Granting schema permissions...")
        await conn.execute('GRANT USAGE ON SCHEMA "common" TO common_user')
        await conn.execute('GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA "common" TO common_user')
        await conn.execute('GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA "common" TO common_user')
        await conn.execute('GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA "common" TO common_user')
        
        # Set default privileges
        print("   Setting default privileges...")
        await conn.execute('ALTER DEFAULT PRIVILEGES IN SCHEMA "common" GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO common_user')
        await conn.execute('ALTER DEFAULT PRIVILEGES IN SCHEMA "common" GRANT USAGE, SELECT ON SEQUENCES TO common_user')
        await conn.execute('ALTER DEFAULT PRIVILEGES IN SCHEMA "common" GRANT EXECUTE ON FUNCTIONS TO common_user')
        
        # Verify the user
        user_info = await conn.fetchrow(
            "SELECT usename, usesuper, usecreatedb FROM pg_user WHERE usename = $1",
            "common_user"
        )

        print(f"✅ User configured successfully!")
        print(f"   Username: {user_info['usename']}")
        print(f"   Is superuser: {user_info['usesuper']}")
        
        # Check rds_iam role
        has_iam_role = await conn.fetchval(
            """
            SELECT EXISTS(
                SELECT 1 FROM pg_auth_members am
                JOIN pg_roles r1 ON am.member = r1.oid
                JOIN pg_roles r2 ON am.roleid = r2.oid
                WHERE r1.rolname = $1 AND r2.rolname = 'rds_iam'
            )
            """,
            "common_user"
        )
        
        print(f"   Has rds_iam role: {has_iam_role}")
        
        # Check schema access
        schema_exists = await conn.fetchval(
            "SELECT EXISTS(SELECT 1 FROM information_schema.schemata WHERE schema_name = 'common')"
        )
        print(f"   Common schema exists: {schema_exists}")
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Failed to create common_user: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Main function."""
    print("🚀 Creating common_user on Aurora")
    print("=" * 40)
    
    success = await create_common_user()
    
    if success:
        print("\n✅ common_user created successfully!")
        print("   Now test IAM authentication again.")
        return 0
    else:
        print("\n❌ Failed to create common_user.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
