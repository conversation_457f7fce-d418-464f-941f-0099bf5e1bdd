["tests/test_acd_services.py::TestACDEventProcessor::test_convert_acd_to_agent_event_data_acd_login", "tests/test_acd_services.py::TestACDEventProcessor::test_convert_acd_to_agent_event_data_login", "tests/test_acd_services.py::TestACDEventProcessor::test_convert_acd_to_agent_event_data_logout", "tests/test_acd_services.py::TestACDEventProcessor::test_process_events_batch_empty_list", "tests/test_acd_services.py::TestACDEventProcessor::test_process_events_batch_success", "tests/test_acd_services.py::TestACDEventProcessor::test_process_events_batch_with_errors", "tests/test_acd_services.py::TestACDEventProcessor::test_process_single_event_database_error", "tests/test_acd_services.py::TestACDEventProcessor::test_process_single_event_success", "tests/test_acd_services.py::TestACDEventProcessor::test_process_single_event_with_message_id", "tests/test_acd_services.py::TestACDEventProcessor::test_process_single_event_xml_parse_error", "tests/test_lambda_function.py::TestLambdaFunction::test_get_service", "tests/test_lambda_function.py::TestLambdaFunction::test_lambda_handler_empty_records", "tests/test_lambda_function.py::TestLambdaFunction::test_lambda_handler_exception", "tests/test_lambda_function.py::TestLambdaFunction::test_lambda_handler_success", "tests/test_lambda_function.py::TestLambdaFunction::test_powertools_initialization", "tests/test_lambda_function.py::TestLambdaFunction::test_process_events_async", "tests/test_lambda_handler.py::TestLambdaHandler::test_handler_success", "tests/test_lambda_handler.py::TestLambdaHandler::test_handler_with_duplicates", "tests/test_lambda_handler.py::TestLambdaHandler::test_handler_with_exception", "tests/test_lambda_handler.py::TestLambdaHandler::test_handler_with_failures", "tests/test_lambda_handler.py::TestLambdaHandler::test_handler_with_multiple_records", "tests/test_lambda_handler.py::TestLambdaHandler::test_handler_with_sns_wrapped_message", "tests/test_lambda_handler.py::TestProcessEventsAsync::test_process_batch_events", "tests/test_lambda_handler.py::TestProcessEventsAsync::test_process_single_event_duplicate", "tests/test_lambda_handler.py::TestProcessEventsAsync::test_process_single_event_failure", "tests/test_lambda_handler.py::TestProcessEventsAsync::test_process_single_event_success", "tests/test_models.py::TestAgentEventPayload::test_empty_tenant_name", "tests/test_models.py::TestAgentEventPayload::test_event_type_case_normalization", "tests/test_models.py::TestAgentEventPayload::test_invalid_event_type", "tests/test_models.py::TestAgentEventPayload::test_payload_with_optional_fields", "tests/test_models.py::TestAgentEventPayload::test_string_field_trimming", "tests/test_models.py::TestAgentEventPayload::test_valid_payload_creation", "tests/test_models.py::TestAgentEventPayload::test_whitespace_agent_name", "tests/test_models.py::TestBatchProcessingResult::test_batch_result_creation", "tests/test_models.py::TestBatchProcessingResult::test_perfect_success_rate", "tests/test_models.py::TestBatchProcessingResult::test_success_rate_calculation", "tests/test_models.py::TestBatchProcessingResult::test_success_rate_zero_events", "tests/test_models.py::TestProcessingResult::test_duplicate_result", "tests/test_models.py::TestProcessingResult::test_failed_result", "tests/test_models.py::TestProcessingResult::test_successful_result", "tests/test_models_simple.py::TestAgentEventPayload::test_empty_tenant_name", "tests/test_models_simple.py::TestAgentEventPayload::test_event_type_validation", "tests/test_models_simple.py::TestAgentEventPayload::test_optional_fields", "tests/test_models_simple.py::TestAgentEventPayload::test_valid_payload", "tests/test_models_simple.py::TestLambdaResponse::test_error_response", "tests/test_models_simple.py::TestLambdaResponse::test_response_serialization", "tests/test_models_simple.py::TestLambdaResponse::test_success_response", "tests/test_models_simple.py::TestProcessingResult::test_failed_result", "tests/test_models_simple.py::TestProcessingResult::test_valid_result", "tests/unit/test_dimension_handlers.py::test_command_composition_all_existing", "tests/unit/test_dimension_handlers.py::test_command_composition_all_new", "tests/unit/test_dimension_handlers.py::test_get_or_create_agent_existing", "tests/unit/test_dimension_handlers.py::test_get_or_create_agent_new", "tests/unit/test_dimension_handlers.py::test_get_or_create_agent_no_role", "tests/unit/test_dimension_handlers.py::test_get_or_create_ring_group_existing", "tests/unit/test_dimension_handlers.py::test_get_or_create_ring_group_new", "tests/unit/test_dimension_handlers.py::test_get_or_create_ring_group_no_uri", "tests/unit/test_dimension_handlers.py::test_get_or_create_tenant_existing", "tests/unit/test_dimension_handlers.py::test_get_or_create_tenant_new", "tests/unit/test_dimension_handlers.py::test_get_or_create_tenant_no_display_name", "tests/unit/test_process_agent_event_handler.py::test_process_agent_event_duplicate", "tests/unit/test_process_agent_event_handler.py::test_process_agent_event_existing_agent", "tests/unit/test_process_agent_event_handler.py::test_process_agent_event_existing_tenant", "tests/unit/test_process_agent_event_handler.py::test_process_agent_event_hash_generation", "tests/unit/test_process_agent_event_handler.py::test_process_agent_event_invalid_xml", "tests/unit/test_process_agent_event_handler.py::test_process_agent_event_missing_required_fields", "tests/unit/test_process_agent_event_handler.py::test_process_agent_event_success", "tests/unit/test_process_agent_event_handler.py::test_process_agent_event_timezone_conversion", "tests/unit/test_process_agent_event_handler.py::test_process_agent_event_with_ring_group"]