locals {
  lambda_zip_path    = abspath("${var.project_dir}/artifacts/${var.lambda_zip_file_name}")
  enable_dlq_trigger = var.lambda_trigger_dlq_name != "" && var.lambda_trigger_dlq_name != null
  enable_sqs_trigger = var.lambda_trigger_queue_name != "" && var.lambda_trigger_queue_name != null
  # Compact to remove nulls
  queue_arns = compact([
    local.enable_sqs_trigger ? "arn:${data.aws_partition.current.partition}:sqs:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:${var.lambda_trigger_queue_name}" : null,
    local.enable_dlq_trigger ? "arn:${data.aws_partition.current.partition}:sqs:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:${var.lambda_trigger_dlq_name}" : null
  ])
}
