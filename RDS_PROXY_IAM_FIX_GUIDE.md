# RDS Proxy IAM Authentication Fix Guide

## Problem
Your RDS Proxy IAM authentication is failing with the error:
```
This RDS proxy has no credentials for the role common_user. Check the credentials for this role and try again.
```

## Root Cause
The issue is in your RDS Proxy configuration. Both auth blocks are using secrets that contain the master user credentials (`Allerium`), but you're trying to connect as `common_user`. RDS Proxy doesn't know how to map your IAM authentication to the `common_user` database user.

## Current Configuration Issues
1. **IAM Auth Secret**: Points to a secret with `username: Allerium`
2. **Connection Attempt**: Trying to connect as `common_user`
3. **Mapping Problem**: RDS Proxy can't map IAM identity to `common_user`

## Solution Steps

### Step 1: Create Database User (Already Done)
You've already created the `common_user` with proper IAM permissions. The SQL script has been updated with additional permissions for the `memo` schema.

### Step 2: Create New Secret for IAM Authentication
You need to create a separate AWS Secrets Manager secret specifically for IAM authentication:

```bash
# Create the secret
aws secretsmanager create-secret \
    --name "dev-au-smartanalytics-common-aurora-credentials-iam" \
    --description "Aurora database credentials for IAM authentication via RDS Proxy" \
    --secret-string '{"username":"common_user","password":"not-used-for-iam-auth"}' \
    --region ap-southeast-2

# Note: The password field is required by the secret format but not used for IAM auth
```

### Step 3: Update Terraform Configuration
Replace your current RDS Proxy configuration with the updated version that includes:

1. **New Secret Resource**: `aws_secretsmanager_secret.aurora_db_credentials_iam`
2. **Updated Auth Block**: IAM auth now points to the new secret with `common_user`
3. **Updated IAM Policies**: Proxy role can access the new secret

### Step 4: Apply Terraform Changes
```bash
terraform plan
terraform apply
```

### Step 5: Update Your Connection Code
Make sure your connection code is using the correct username:

```python
# For IAM authentication, use common_user
username = "common_user"
host = "dev-au-smartanalytics-common-aurora-db-proxy.proxy-c3atdbtyiqrw.ap-southeast-2.rds.amazonaws.com"
port = 5432
database = "prod"
region = "ap-southeast-2"

# Generate IAM token
token = boto3.client('rds').generate_db_auth_token(
    DBHostname=host,
    Port=port,
    DBUsername=username,
    Region=region
)
```

## Key Changes in Terraform

### Before (Problematic)
```hcl
auth {
  description = "RDS Proxy Auth"
  iam_auth    = "REQUIRED"
  secret_arn  = aws_secretsmanager_secret.aurora_db_credentials_for_db_proxy.arn
  # This secret contains username: "Allerium"
}
```

### After (Fixed)
```hcl
auth {
  auth_scheme = "SECRETS"
  description = "RDS Proxy IAM Auth"
  iam_auth    = "REQUIRED"
  secret_arn  = aws_secretsmanager_secret.aurora_db_credentials_iam.arn
  # This secret contains username: "common_user"
}
```

## Verification Steps

### 1. Check Secret Contents
```bash
aws secretsmanager get-secret-value \
    --secret-id "dev-au-smartanalytics-common-aurora-credentials-iam" \
    --region ap-southeast-2
```

### 2. Test IAM Authentication
Run your test script again after applying the changes.

### 3. Check RDS Proxy Configuration
```bash
aws rds describe-db-proxies \
    --db-proxy-name "dev-au-smartanalytics-common-aurora-db-proxy" \
    --region ap-southeast-2
```

## Expected Results
After implementing these changes:
1. ✅ Password authentication will continue to work with `Allerium` user
2. ✅ IAM authentication will work with `common_user`
3. ✅ Both authentication methods will work through the proxy
4. ✅ Direct database connections will continue to work as before

## Troubleshooting
If you still have issues after applying these changes:

1. **Check IAM Permissions**: Ensure your IAM user/role has `rds-db:connect` permission
2. **Verify Database User**: Run the verification queries in the SQL script
3. **Check Security Groups**: Ensure your client can reach the proxy
4. **Review CloudWatch Logs**: Check RDS Proxy logs for detailed error messages
