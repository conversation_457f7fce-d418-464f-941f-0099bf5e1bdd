"""AWS service configuration."""

from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class AWSConfig(BaseSettings):
    """AWS service configuration."""

    # Core AWS settings
    region: str = Field(default="us-east-1", description="AWS region")

    account_id: str | None = Field(default=None, description="AWS account ID")

    # Secrets Manager settings
    secrets_manager_endpoint: str | None = Field(
        default=None, description="Custom Secrets Manager endpoint URL"
    )

    # Parameter Store settings
    parameter_store_prefix: str = Field(
        default="/smartanalytics",
        description="Parameter Store prefix for configuration",
    )

    parameter_store_endpoint: str | None = Field(
        default=None, description="Custom Parameter Store endpoint URL"
    )

    # Lambda settings
    lambda_timeout: int = Field(
        default=300, ge=1, le=900, description="Lambda function timeout in seconds"
    )

    lambda_memory: int = Field(
        default=1024, ge=128, le=10240, description="Lambda function memory in MB"
    )

    lambda_reserved_concurrency: int | None = Field(
        default=None, ge=0, description="Lambda reserved concurrency limit"
    )

    # CloudWatch settings
    cloudwatch_namespace: str = Field(
        default="SmartAnalytics", description="CloudWatch metrics namespace"
    )

    cloudwatch_log_retention_days: int = Field(
        default=30, ge=1, description="CloudWatch log retention in days"
    )

    # X-Ray settings
    xray_tracing_enabled: bool = Field(default=True, description="Enable X-Ray tracing")

    model_config = SettingsConfigDict(
        env_prefix="AWS__",
        case_sensitive=False,
        str_strip_whitespace=True,
        validate_assignment=True,
        extra="ignore",  # Changed from "forbid" to "ignore" to allow extra env vars
    )

    def get_secrets_manager_config(self) -> dict[str, str]:
        """Get Secrets Manager client configuration."""
        config = {"region_name": self.region}

        if self.secrets_manager_endpoint:
            config["endpoint_url"] = self.secrets_manager_endpoint

        return config

    def get_parameter_store_config(self) -> dict[str, str]:
        """Get Parameter Store (SSM) client configuration."""
        config = {"region_name": self.region}

        if self.parameter_store_endpoint:
            config["endpoint_url"] = self.parameter_store_endpoint

        return config

    def get_cloudwatch_config(self) -> dict[str, str]:
        """Get CloudWatch client configuration."""
        return {
            "region_name": self.region,
            "namespace": self.cloudwatch_namespace,
        }
