# CloudWatch Alarms Module

This Terraform module creates CloudWatch alarms for monitoring AWS Lambda functions. It provides proactive monitoring for errors, performance issues, and anomalous behavior.

## Features

- **Error Monitoring**: Detects errors and sends notifications
- **Performance Monitoring**: Duration warnings for functions taking too long
- **Anomaly Detection**: Machine learning-based detection of unusual invocation patterns
- **SNS Integration**: Automatic notifications to Slack or other endpoints

## Alarms Created

### 1. Lambda Errors Alarm
- **Metric**: % Error Rate = (SUM(Errors) / SUM(Invocations)) * 100
- **Threshold**: > 1% error
- **Period**: 5 minutes
- **Purpose**: Immediate notification of any function failures

### 2. Lambda Duration Warning
- **Metric**: AWS/Lambda Duration (p90)
- **Threshold**: > 80% of timeout
- **Period**: 5 minutes
- **Purpose**: Early warning of performance degradation

### 3. Lambda Invocation Anomaly Detection
- **Metric**: AWS/Lambda Invocations (SUM)
- **Detection**: Anomaly detection band (2 standard deviations)
- **Period**: 5 minutes
- **Purpose**: Detect unusual traffic patterns or processing issues

### 4. Lambda Throttles Alarm
- **Metric**: AWS/Lambda Throttles (SUM)
- **Threshold**: > 1
- **Period**: 5 minutes
- **Purpose**: Detect throttling events due to high concurrency or other issues

## Usage

```hcl
module "lambda_alarms" {
  source = "./modules/alarms"

  lambda_function_name = "lambda-function"
  sns_topic_name      = "slack-alerts"
  evaluation_periods  = 3

  tags = {
    Environment = "prod"
    Service     = "smartanalytics"
    Team        = "Smart Analytics Team"
  }
}
```

## Requirements

| Name | Version |
|------|---------|
| terraform | >= 1.6 |
| aws | >= 5.0 |

## Providers

| Name | Version |
|------|---------|
| aws | >= 5.0 |

## Resources

| Name | Type |
|------|------|
| aws_cloudwatch_metric_alarm.lambda_processors_errors | resource |
| aws_cloudwatch_metric_alarm.lambda_processors_invocations | resource |
| aws_cloudwatch_metric_alarm.lambda_processors_duration_warn_alarm | resource |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| lambda_function_name | Name of the Lambda function to monitor | `string` | n/a | yes |
| sns_topic_name | Name of the SNS topic for alarm notifications | `string` | n/a | no |
| evaluation_periods | Number of periods to evaluate for the alarm | `number` | `1` | no |
| lambda_timeout | Lambda timeout in seconds. | `number` | `60` | no |
| period | Duration in seconds for each evaluation period | `number` | `300` | no |
| threshold | Percentage threshold for decrease in invocations | `number` | `50` | no |
| tags | List of key/value pairs for tags | `map(string)` | n/a | yes |

## Outputs

| Name | Description |
|------|-------------|
| lambda_errors_alarm_name | Name of the Lambda errors alarm |
| lambda_errors_alarm_arn | ARN of the Lambda errors alarm |
| lambda_invocations_alarm_name | Name of the Lambda invocations decrease alarm |
| lambda_invocations_alarm_arn | ARN of the Lambda invocations decrease alarm |
| lambda_duration_alarm_name | Name of the Lambda duration warning alarm |
| lambda_duration_alarm_arn | ARN of the Lambda duration warning alarm |
| lambda_throttles_alarm_name | Name of the Lambda throttles alarm |
| lambda_throttles_alarm_arn | ARN of the Lambda throttles alarm |

## Alarm States

- **OK**: All metrics are within normal ranges
- **ALARM**: One or more thresholds have been breached
- **INSUFFICIENT_DATA**: Not enough data points to evaluate

## Best Practices

1. **SNS Topic**: Ensure the SNS topic exists and has proper subscribers
2. **Evaluation Periods**: Use 2+ periods to reduce false positives
3. **Thresholds**: Adjust based on your application's normal behavior
4. **Tags**: Include consistent tagging for cost allocation and management

## Integration

This module is designed to be used with Lambda functions and requires:
- Existing Lambda function to monitor
- SNS topic for notifications
- Appropriate CloudWatch permissions
