# RDS Proxy IAM Authentication Fix - Summary

## Problem Solved
Fixed the RDS Proxy IAM authentication issue where connections failed with:
- "This RDS proxy has no credentials for the role common_user"
- "The database user 'Allerium' was found in multiple DB proxy authentication entries"

## Root Cause
The RDS Proxy had two authentication configurations but both were pointing to secrets with the same database username (`Allerium`), causing conflicts.

## Solution Applied

### 1. Fixed Secret Configuration
**Before:**
- Password Auth Secret: `{"username": "Allerium", "password": "72bOkOq23DL3MuUa"}`
- IAM Auth Secret: `{"username": "Allerium", "password": "72bOkOq23DL3MuUa"}` ❌ Same user!

**After:**
- Password Auth Secret: `{"username": "Allerium", "password": "72bOkOq23DL3MuUa"}`
- IAM Auth Secret: `{"username": "common_user", "password": "dummy-not-used-for-iam"}` ✅ Different user!

### 2. Commands Used to Fix

#### Step 1: Fix the IAM secret content
```bash
aws secretsmanager update-secret \
    --secret-id "arn:aws:secretsmanager:ap-southeast-2:024660257967:secret:dev-au-smartanalytics-common-aurora-credentials-for-db-proxy-f3jyke" \
    --region "ap-southeast-2" \
    --secret-string '{"username":"common_user","password":"dummy-not-used-for-iam"}'
```

#### Step 2: Force proxy configuration refresh
```bash
aws rds modify-db-proxy \
    --db-proxy-name "dev-au-smartanalytics-common-aurora-db-proxy" \
    --region "ap-southeast-2" \
    --auth file://proxy_auth_config.json
```

Where `proxy_auth_config.json` contains:
```json
[
    {
        "Description": "RDS Proxy Password Auth",
        "AuthScheme": "SECRETS",
        "SecretArn": "arn:aws:secretsmanager:ap-southeast-2:024660257967:secret:dev-au-smartanalytics-common-aurora-credentials-87Gb1f",
        "IAMAuth": "DISABLED",
        "ClientPasswordAuthType": "POSTGRES_SCRAM_SHA_256"
    },
    {
        "Description": "RDS Proxy IAM Auth",
        "AuthScheme": "SECRETS",
        "SecretArn": "arn:aws:secretsmanager:ap-southeast-2:024660257967:secret:dev-au-smartanalytics-common-aurora-credentials-for-db-proxy-f3jyke",
        "IAMAuth": "REQUIRED",
        "ClientPasswordAuthType": "POSTGRES_SCRAM_SHA_256"
    }
]
```

### 3. Database User Setup (Already Correct)
The `common_user` database user was properly configured with:
- ✅ `rds_iam` role granted
- ✅ Proper schema permissions
- ✅ Direct database IAM authentication working

## Current Status

### ✅ Working
- **Direct Database IAM Authentication**: `common_user` can connect directly to Aurora cluster with IAM tokens
- **Password Authentication via Proxy**: `Allerium` user works with username/password through proxy
- **Secret Configuration**: Both secrets have correct format and different usernames

### 🔄 In Progress
- **Proxy IAM Authentication**: Getting "connection was closed in the middle of operation" error
- This suggests the proxy now recognizes `common_user` but there may be a network/connection issue

## Next Steps for Complete Resolution

### 1. Wait for Proxy Cache Refresh
RDS Proxy may take up to 5-10 minutes to fully refresh secret values and clear internal caches.

### 2. Check Network Configuration
If the issue persists, verify:
- Security groups allow traffic between proxy and database
- Subnet routing is correct
- No network ACLs blocking connections

### 3. Monitor CloudWatch Logs
Check `/aws/rds/proxy/dev-au-smartanalytics-common-aurora-db-proxy` for new error messages after the configuration changes.

### 4. Test Again
Run the test script periodically:
```bash
python test_iam_token.py
```

## Key Learnings

1. **RDS Proxy requires unique usernames** across all auth configurations
2. **Secret format must be exact**: `{"username": "...", "password": "..."}`
3. **Proxy caching** can delay configuration changes by several minutes
4. **Force refresh** by modifying proxy configuration helps apply secret changes
5. **CloudWatch logs** are essential for debugging proxy authentication issues

## Terraform Prevention

To prevent this issue in Terraform, ensure:
```hcl
# Password auth secret
resource "aws_secretsmanager_secret_version" "password_auth" {
  secret_string = jsonencode({
    username = "Allerium"  # Master user
    password = var.master_password
  })
}

# IAM auth secret  
resource "aws_secretsmanager_secret_version" "iam_auth" {
  secret_string = jsonencode({
    username = "common_user"  # IAM user - DIFFERENT from password auth
    password = "dummy-not-used"
  })
}
```
