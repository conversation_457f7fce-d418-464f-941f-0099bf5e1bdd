#!/bin/bash

echo "🔍 Checking RDS Proxy Secret Configuration"
echo "=========================================="

# Check the current secret value
echo "Current secret content:"
aws secretsmanager get-secret-value \
  --secret-id "dev-au-smartanalytics-common-aurora-credentials-for-db-proxy" \
  --region ap-southeast-2 \
  --query 'SecretString' \
  --output text | jq .

echo ""
echo "🚨 IMPORTANT: The secret should contain your MASTER database user credentials, NOT the IAM user credentials!"
echo ""
echo "Expected format:"
echo '{'
echo '  "username": "your_master_username",'
echo '  "password": "your_master_password"'
echo '}'
echo ""
echo "The master username is typically what you set when creating the Aurora cluster."
echo "Check your Terraform variable: aurora_master_username"
