["lambdas/acd-event-processor/test_rds_proxy_connection.py::test_iam_connection", "lambdas/acd-event-processor/test_rds_proxy_connection.py::test_password_connection", "lambdas/acd-event-processor/tests/test_lambda_handler.py::TestLambdaHandler::test_handler_success", "lambdas/acd-event-processor/tests/test_lambda_handler.py::TestLambdaHandler::test_handler_with_duplicates", "lambdas/acd-event-processor/tests/test_lambda_handler.py::TestLambdaHandler::test_handler_with_exception", "lambdas/acd-event-processor/tests/test_lambda_handler.py::TestLambdaHandler::test_handler_with_failures", "lambdas/acd-event-processor/tests/test_lambda_handler.py::TestLambdaHandler::test_handler_with_multiple_records", "lambdas/acd-event-processor/tests/test_lambda_handler.py::TestLambdaHandler::test_handler_with_sns_wrapped_message", "lambdas/acd-event-processor/tests/test_lambda_handler.py::TestProcessEventsAsync::test_process_batch_events", "lambdas/acd-event-processor/tests/test_lambda_handler.py::TestProcessEventsAsync::test_process_single_event_duplicate", "lambdas/acd-event-processor/tests/test_lambda_handler.py::TestProcessEventsAsync::test_process_single_event_failure", "lambdas/acd-event-processor/tests/test_lambda_handler.py::TestProcessEventsAsync::test_process_single_event_success", "lambdas/acd-event-processor/tests/unit/test_dimension_handlers.py::test_command_composition_all_existing", "lambdas/acd-event-processor/tests/unit/test_dimension_handlers.py::test_command_composition_all_new", "lambdas/acd-event-processor/tests/unit/test_dimension_handlers.py::test_get_or_create_agent_existing", "lambdas/acd-event-processor/tests/unit/test_dimension_handlers.py::test_get_or_create_agent_new", "lambdas/acd-event-processor/tests/unit/test_dimension_handlers.py::test_get_or_create_agent_no_role", "lambdas/acd-event-processor/tests/unit/test_dimension_handlers.py::test_get_or_create_ring_group_existing", "lambdas/acd-event-processor/tests/unit/test_dimension_handlers.py::test_get_or_create_ring_group_new", "lambdas/acd-event-processor/tests/unit/test_dimension_handlers.py::test_get_or_create_ring_group_no_uri", "lambdas/acd-event-processor/tests/unit/test_dimension_handlers.py::test_get_or_create_tenant_existing", "lambdas/acd-event-processor/tests/unit/test_dimension_handlers.py::test_get_or_create_tenant_new", "lambdas/acd-event-processor/tests/unit/test_dimension_handlers.py::test_get_or_create_tenant_no_display_name", "lambdas/acd-event-processor/tests/unit/test_process_agent_event_handler.py::test_process_agent_event_duplicate", "lambdas/acd-event-processor/tests/unit/test_process_agent_event_handler.py::test_process_agent_event_existing_agent", "lambdas/acd-event-processor/tests/unit/test_process_agent_event_handler.py::test_process_agent_event_existing_tenant", "lambdas/acd-event-processor/tests/unit/test_process_agent_event_handler.py::test_process_agent_event_hash_generation", "lambdas/acd-event-processor/tests/unit/test_process_agent_event_handler.py::test_process_agent_event_invalid_xml", "lambdas/acd-event-processor/tests/unit/test_process_agent_event_handler.py::test_process_agent_event_missing_required_fields", "lambdas/acd-event-processor/tests/unit/test_process_agent_event_handler.py::test_process_agent_event_success", "lambdas/acd-event-processor/tests/unit/test_process_agent_event_handler.py::test_process_agent_event_timezone_conversion", "lambdas/acd-event-processor/tests/unit/test_process_agent_event_handler.py::test_process_agent_event_with_ring_group", "lambdas/agent-event-processor/tests/integration/test_lambda_integration.py::TestLambdaIntegration::test_batch_event_processing", "lambdas/agent-event-processor/tests/integration/test_lambda_integration.py::TestLambdaIntegration::test_busied_out_event_processing", "lambdas/agent-event-processor/tests/integration/test_lambda_integration.py::TestLambdaIntegration::test_database_connectivity", "lambdas/agent-event-processor/tests/integration/test_lambda_integration.py::TestLambdaIntegration::test_invalid_xml_handling", "lambdas/agent-event-processor/tests/integration/test_lambda_integration.py::TestLambdaIntegration::test_lambda_response_format", "lambdas/agent-event-processor/tests/integration/test_lambda_integration.py::TestLambdaIntegration::test_lambda_timeout_handling", "lambdas/agent-event-processor/tests/integration/test_lambda_integration.py::TestLambdaIntegration::test_login_event_processing", "lambdas/agent-event-processor/tests/integration/test_lambda_integration.py::TestLambdaIntegration::test_missing_required_fields", "lambdas/agent-event-processor/tests/unit/test_event_processor.py::TestEventProcessor::test_insert_fact_record_duplicate", "lambdas/agent-event-processor/tests/unit/test_event_processor.py::TestEventProcessor::test_insert_fact_record_success", "lambdas/agent-event-processor/tests/unit/test_event_processor.py::TestEventProcessor::test_process_single_event_failure", "lambdas/agent-event-processor/tests/unit/test_event_processor.py::TestEventProcessor::test_process_single_event_success", "lambdas/agent-event-processor/tests/unit/test_event_processor.py::TestEventProcessor::test_resolve_dimension_keys", "lambdas/agent-event-processor/tests/unit/test_event_processor.py::TestEventProcessor::test_transform_to_fact_data", "lambdas/agent-event-processor/tests/unit/test_lambda_function.py::TestLambdaHandler::test_acd_login_event_processing", "lambdas/agent-event-processor/tests/unit/test_lambda_function.py::TestLambdaHandler::test_batch_processing_multiple_events", "lambdas/agent-event-processor/tests/unit/test_lambda_function.py::TestLambdaHandler::test_empty_records", "lambdas/agent-event-processor/tests/unit/test_lambda_function.py::TestLambdaHandler::test_invalid_xml_handling", "lambdas/agent-event-processor/tests/unit/test_lambda_function.py::TestLambdaHandler::test_partial_batch_failure", "lambdas/agent-event-processor/tests/unit/test_lambda_function.py::TestLambdaHandler::test_sns_wrapped_message", "lambdas/agent-event-processor/tests/unit/test_lambda_function.py::TestLambdaHandler::test_successful_login_event_processing", "lambdas/agent-event-processor/tests/unit/test_models.py::TestAgentEvent::test_acd_event_validation", "lambdas/agent-event-processor/tests/unit/test_models.py::TestAgentEvent::test_extra_fields_rejected", "lambdas/agent-event-processor/tests/unit/test_models.py::TestAgentEvent::test_invalid_agent_uri", "lambdas/agent-event-processor/tests/unit/test_models.py::TestAgentEvent::test_missing_required_fields", "lambdas/agent-event-processor/tests/unit/test_models.py::TestAgentEvent::test_timestamp_parsing", "lambdas/agent-event-processor/tests/unit/test_models.py::TestAgentEvent::test_valid_acd_login_event", "lambdas/agent-event-processor/tests/unit/test_models.py::TestAgentEvent::test_valid_login_event", "lambdas/agent-event-processor/tests/unit/test_models.py::TestDatabaseCredentials::test_empty_host", "lambdas/agent-event-processor/tests/unit/test_models.py::TestDatabaseCredentials::test_invalid_port", "lambdas/agent-event-processor/tests/unit/test_models.py::TestDatabaseCredentials::test_valid_credentials", "lambdas/agent-event-processor/tests/unit/test_models.py::TestDimensionKeys::test_invalid_date_key", "lambdas/agent-event-processor/tests/unit/test_models.py::TestDimensionKeys::test_invalid_time_key", "lambdas/agent-event-processor/tests/unit/test_models.py::TestDimensionKeys::test_valid_dimension_keys", "layers/domain/python/tests/handlers/test_dimension_handlers.py::test_command_composition_all_existing", "layers/domain/python/tests/handlers/test_dimension_handlers.py::test_command_composition_all_new", "layers/domain/python/tests/handlers/test_dimension_handlers.py::test_get_or_create_agent_existing", "layers/domain/python/tests/handlers/test_dimension_handlers.py::test_get_or_create_agent_new", "layers/domain/python/tests/handlers/test_dimension_handlers.py::test_get_or_create_agent_no_role", "layers/domain/python/tests/handlers/test_dimension_handlers.py::test_get_or_create_ring_group_existing", "layers/domain/python/tests/handlers/test_dimension_handlers.py::test_get_or_create_ring_group_new", "layers/domain/python/tests/handlers/test_dimension_handlers.py::test_get_or_create_ring_group_no_uri", "layers/domain/python/tests/handlers/test_dimension_handlers.py::test_get_or_create_tenant_existing", "layers/domain/python/tests/handlers/test_dimension_handlers.py::test_get_or_create_tenant_new", "layers/domain/python/tests/handlers/test_dimension_handlers.py::test_get_or_create_tenant_no_display_name", "layers/domain/python/tests/handlers/test_event_handlers.py::test_process_agent_event_duplicate", "layers/domain/python/tests/handlers/test_event_handlers.py::test_process_agent_event_existing_agent", "layers/domain/python/tests/handlers/test_event_handlers.py::test_process_agent_event_existing_tenant", "layers/domain/python/tests/handlers/test_event_handlers.py::test_process_agent_event_hash_generation", "layers/domain/python/tests/handlers/test_event_handlers.py::test_process_agent_event_invalid_xml", "layers/domain/python/tests/handlers/test_event_handlers.py::test_process_agent_event_missing_required_fields", "layers/domain/python/tests/handlers/test_event_handlers.py::test_process_agent_event_success", "layers/domain/python/tests/handlers/test_event_handlers.py::test_process_agent_event_timezone_conversion", "layers/domain/python/tests/handlers/test_event_handlers.py::test_process_agent_event_with_ring_group", "layers/domain/python/tests/test_agent_event.py::TestAgentEvent::test_agent_event_has_ring_group", "layers/domain/python/tests/test_agent_event.py::TestAgentEvent::test_agent_event_is_login_event", "layers/domain/python/tests/test_agent_event.py::TestAgentEvent::test_agent_event_is_logout_event", "layers/domain/python/tests/test_agent_event.py::TestAgentEvent::test_agent_event_to_dict", "layers/domain/python/tests/test_agent_event.py::TestAgentEvent::test_agent_event_validation_empty_tenant_name", "layers/domain/python/tests/test_agent_event.py::TestAgentEvent::test_agent_event_validation_invalid_hash_length", "layers/domain/python/tests/test_agent_event.py::TestAgentEvent::test_create_valid_agent_event", "layers/domain/python/tests/test_agent_event.py::TestAgentEventBatch::test_batch_validation_empty_events", "layers/domain/python/tests/test_agent_event.py::TestAgentEventBatch::test_batch_validation_too_many_events", "layers/domain/python/tests/test_agent_event.py::TestAgentEventBatch::test_create_valid_batch", "layers/domain/python/tests/test_commands.py::TestCommands::test_get_or_create_agent_command", "layers/domain/python/tests/test_commands.py::TestCommands::test_get_or_create_ring_group_command", "layers/domain/python/tests/test_commands.py::TestCommands::test_get_or_create_tenant_command", "layers/domain/python/tests/test_commands.py::TestCommands::test_get_or_create_tenant_command_with_display_name", "layers/domain/python/tests/test_commands.py::TestCommands::test_process_agent_event_command", "layers/domain/python/tests/test_commands.py::TestCommands::test_process_agent_event_command_with_timezone", "layers/domain/python/tests/test_exceptions.py::TestExceptions::test_domain_error", "layers/domain/python/tests/test_exceptions.py::TestExceptions::test_domain_error_to_dict", "layers/domain/python/tests/test_exceptions.py::TestExceptions::test_domain_error_with_context", "layers/domain/python/tests/test_exceptions.py::TestExceptions::test_event_processing_error", "layers/domain/python/tests/test_exceptions.py::TestExceptions::test_event_processing_error_with_event_data", "layers/infrastructure/python/tests/test_database_connection.py::TestDatabaseConnection::test_database_connection_close", "layers/infrastructure/python/tests/test_database_connection.py::TestDatabaseConnection::test_database_connection_connect", "layers/infrastructure/python/tests/test_database_connection.py::TestDatabaseConnection::test_database_connection_get_engine", "layers/infrastructure/python/tests/test_database_connection.py::TestDatabaseConnection::test_database_connection_initialization", "layers/infrastructure/python/tests/test_database_connection.py::TestDatabaseConnection::test_database_connection_test", "layers/infrastructure/python/tests/test_database_connection.py::TestDatabaseConnection::test_get_database_connection_singleton", "layers/infrastructure/python/tests/test_database_models.py::TestDatabaseModels::test_all_expected_tables_exist", "layers/infrastructure/python/tests/test_database_models.py::TestDatabaseModels::test_dim_agent_event_model_creation", "layers/infrastructure/python/tests/test_database_models.py::TestDatabaseModels::test_dim_agent_event_with_ring_group", "layers/infrastructure/python/tests/test_database_models.py::TestDatabaseModels::test_dim_agent_model_creation", "layers/infrastructure/python/tests/test_database_models.py::TestDatabaseModels::test_dim_ring_group_model_creation", "layers/infrastructure/python/tests/test_database_models.py::TestDatabaseModels::test_dim_tenant_model_creation", "layers/infrastructure/python/tests/test_database_models.py::TestDatabaseModels::test_dim_tenant_unique_constraint", "layers/infrastructure/python/tests/test_database_models.py::TestDatabaseModels::test_metadata_schema_configuration", "layers/infrastructure/python/tests/test_database_models.py::TestDatabaseModels::test_scd_type2_pattern_support", "layers/infrastructure/python/tests/test_models.py::TestDatabaseModels::test_dim_agent_creation", "layers/infrastructure/python/tests/test_models.py::TestDatabaseModels::test_dim_agent_event_creation", "layers/infrastructure/python/tests/test_models.py::TestDatabaseModels::test_dim_ring_group_creation", "layers/infrastructure/python/tests/test_models.py::TestDatabaseModels::test_dim_tenant_creation", "layers/infrastructure/python/tests/test_models.py::TestDatabaseModels::test_model_constraints", "layers/infrastructure/python/tests/test_models.py::TestDatabaseModels::test_model_defaults", "layers/infrastructure/python/tests/test_models.py::TestDatabaseModels::test_model_relationships", "layers/infrastructure/python/tests/test_repositories.py::TestAgentEventRepository::test_create_batch_events", "layers/infrastructure/python/tests/test_repositories.py::TestAgentEventRepository::test_create_event_with_ring_group", "layers/infrastructure/python/tests/test_repositories.py::TestAgentEventRepository::test_create_event_without_ring_group", "layers/infrastructure/python/tests/test_repositories.py::TestAgentEventRepository::test_get_by_hash_found", "layers/infrastructure/python/tests/test_repositories.py::TestAgentEventRepository::test_get_by_hash_not_found", "layers/infrastructure/python/tests/test_repositories.py::TestAgentEventRepository::test_save_batch_events", "layers/infrastructure/python/tests/test_repositories.py::TestAgentEventRepository::test_save_event_without_ring_group", "layers/infrastructure/python/tests/test_repositories.py::TestAgentRepository::test_create_agent", "layers/infrastructure/python/tests/test_repositories.py::TestAgentRepository::test_get_by_name_and_tenant_found", "layers/infrastructure/python/tests/test_repositories.py::TestAgentRepository::test_get_by_tenant_and_name_found", "layers/infrastructure/python/tests/test_repositories.py::TestAgentRepository::test_get_or_create_new_agent", "layers/infrastructure/python/tests/test_repositories.py::TestRepositories::test_agent_event_repository_add", "layers/infrastructure/python/tests/test_repositories.py::TestRepositories::test_agent_event_repository_exists_by_hash", "layers/infrastructure/python/tests/test_repositories.py::TestRepositories::test_agent_repository_get_or_create", "layers/infrastructure/python/tests/test_repositories.py::TestRepositories::test_ring_group_repository_get_or_create", "layers/infrastructure/python/tests/test_repositories.py::TestRepositories::test_tenant_repository_get_or_create", "layers/infrastructure/python/tests/test_repositories.py::TestRingGroupRepository::test_create_ring_group", "layers/infrastructure/python/tests/test_repositories.py::TestRingGroupRepository::test_get_by_name_and_tenant_found", "layers/infrastructure/python/tests/test_repositories.py::TestRingGroupRepository::test_get_by_tenant_and_name_found", "layers/infrastructure/python/tests/test_repositories.py::TestRingGroupRepository::test_get_or_create_with_uri_extraction", "layers/infrastructure/python/tests/test_repositories.py::TestTenantRepository::test_create_tenant", "layers/infrastructure/python/tests/test_repositories.py::TestTenantRepository::test_get_by_name_found", "layers/infrastructure/python/tests/test_repositories.py::TestTenantRepository::test_get_by_name_not_found", "layers/infrastructure/python/tests/test_repositories.py::TestTenantRepository::test_get_or_create_existing_tenant", "layers/infrastructure/python/tests/test_repositories.py::TestTenantRepository::test_get_or_create_new_tenant", "layers/infrastructure/python/tests/test_repositroy.py::TestRepositories::test_agent_event_repository_add", "layers/infrastructure/python/tests/test_repositroy.py::TestRepositories::test_agent_repository_get_or_create", "layers/infrastructure/python/tests/test_repositroy.py::TestRepositories::test_ring_group_repository_get_or_create", "layers/infrastructure/python/tests/test_repositroy.py::TestRepositories::test_tenant_repository_get_or_create", "layers/infrastructure/python/tests/test_unit_of_work.py::TestSqlAlchemyUnitOfWork::test_unit_of_work_context_manager_exception", "layers/infrastructure/python/tests/test_unit_of_work.py::TestSqlAlchemyUnitOfWork::test_unit_of_work_context_manager_success", "layers/infrastructure/python/tests/test_unit_of_work.py::TestSqlAlchemyUnitOfWork::test_unit_of_work_initialization", "layers/utilities/python/tests/test_config.py::TestDatabaseConfig::test_database_config_validation_error", "layers/utilities/python/tests/test_config.py::TestDatabaseConfig::test_database_config_with_secrets_arn", "layers/utilities/python/tests/test_config.py::TestDatabaseConfig::test_database_config_with_username", "layers/utilities/python/tests/test_config.py::TestDatabaseConfig::test_database_url_generation", "layers/utilities/python/tests/test_config.py::TestDatabaseConfig::test_database_url_without_password", "layers/utilities/python/tests/test_config.py::TestLoggingConfig::test_log_format_generation", "layers/utilities/python/tests/test_config.py::TestLoggingConfig::test_logging_config_custom_values", "layers/utilities/python/tests/test_config.py::TestLoggingConfig::test_logging_config_defaults", "layers/utilities/python/tests/test_config.py::TestLoggingConfig::test_third_party_logger_levels", "layers/utilities/python/tests/test_config.py::TestSettings::test_settings_feature_flags", "layers/utilities/python/tests/test_config.py::TestSettings::test_settings_with_environment_override", "layers/utilities/python/tests/test_config.py::TestSettings::test_settings_with_minimal_config", "layers/utilities/python/tests/test_datetime_and_hash_utils.py::TestDatetimeHelper::test_get_client_timezone", "layers/utilities/python/tests/test_datetime_and_hash_utils.py::TestDatetimeHelper::test_get_current_time", "layers/utilities/python/tests/test_datetime_and_hash_utils.py::TestHashHelper::test_generate_event_hash", "layers/utilities/python/tests/test_datetime_and_hash_utils.py::TestHashHelper::test_hash_event", "layers/utilities/python/tests/test_datetime_helper.py::TestDatetimeHelper::test_get_client_timezone", "layers/utilities/python/tests/test_datetime_helper.py::TestDatetimeHelper::test_get_client_timezone_with_different_zones", "layers/utilities/python/tests/test_datetime_helper.py::TestDatetimeHelper::test_get_current_time", "layers/utilities/python/tests/test_hash.py::TestHashHelper::test_different_data_different_hash", "layers/utilities/python/tests/test_hash.py::TestHashHelper::test_generate_event_hash_dict", "layers/utilities/python/tests/test_hash.py::TestHashHelper::test_generate_event_hash_different_order", "layers/utilities/python/tests/test_hash.py::TestHashHelper::test_generate_event_hash_nested_dict", "layers/utilities/python/tests/test_hash.py::TestHashHelper::test_hash_event", "layers/utilities/python/tests/test_hash.py::TestHashHelper::test_hash_event_with_nested_data", "layers/utilities/python/tests/test_hash_helper.py::TestHashDeterminism::test_generate_event_hash_consistency", "layers/utilities/python/tests/test_hash_helper.py::TestHashDeterminism::test_generate_event_hash_deterministic", "layers/utilities/python/tests/test_hash_helper.py::TestHashDeterminism::test_generate_event_hash_different_order", "layers/utilities/python/tests/test_hash_helper.py::TestHashDeterminism::test_generate_event_hash_manual_verification", "layers/utilities/python/tests/test_hash_helper.py::TestHashGeneration::test_generate_event_hash_basic", "layers/utilities/python/tests/test_hash_helper.py::TestHashGeneration::test_generate_event_hash_empty_dict", "layers/utilities/python/tests/test_hash_helper.py::TestHashGeneration::test_generate_event_hash_with_none_values", "layers/utilities/python/tests/test_hash_helper.py::TestHashGeneration::test_hash_event_function", "layers/utilities/python/tests/test_hash_helper.py::TestHashHelper::test_generate_event_hash_basic", "layers/utilities/python/tests/test_hash_helper.py::TestHashHelper::test_generate_event_hash_consistency", "layers/utilities/python/tests/test_hash_helper.py::TestHashHelper::test_generate_event_hash_deterministic", "layers/utilities/python/tests/test_hash_helper.py::TestHashHelper::test_generate_event_hash_different_data", "layers/utilities/python/tests/test_hash_helper.py::TestHashHelper::test_generate_event_hash_empty_dict", "layers/utilities/python/tests/test_hash_helper.py::TestHashHelper::test_generate_event_hash_manual_verification", "layers/utilities/python/tests/test_hash_helper.py::TestHashHelper::test_generate_event_hash_with_nested_data", "layers/utilities/python/tests/test_hash_helper.py::TestHashHelper::test_generate_event_hash_with_none_values", "layers/utilities/python/tests/test_hash_helper.py::TestHashUniqueness::test_different_data_different_hash", "layers/utilities/python/tests/test_hash_helper.py::TestHashUniqueness::test_generate_event_hash_different_data", "layers/utilities/python/tests/test_hash_helper.py::TestHashWithNestedData::test_generate_event_hash_nested_dict", "layers/utilities/python/tests/test_hash_helper.py::TestHashWithNestedData::test_generate_event_hash_with_nested_data", "layers/utilities/python/tests/test_hash_helper.py::TestHashWithNestedData::test_hash_event_with_nested_data", "layers/utilities/python/tests/test_simple.py::TestDatetimeHelper::test_get_client_timezone", "layers/utilities/python/tests/test_simple.py::TestDatetimeHelper::test_get_current_time", "layers/utilities/python/tests/test_simple.py::TestHashHelper::test_generate_event_hash", "layers/utilities/python/tests/test_simple.py::TestHashHelper::test_hash_event", "layers/utilities/python/tests/test_utils.py::TestHashHelper::test_generate_event_hash_dict", "layers/utilities/python/tests/test_utils.py::TestHashHelper::test_generate_event_hash_different_order", "layers/utilities/python/tests/test_utils.py::TestHashHelper::test_generate_event_hash_nested_dict", "layers/utilities/python/tests/test_utils.py::TestHashHelper::test_generate_event_hash_string", "layers/utilities/python/tests/test_utils.py::TestHashHelper::test_hash_event", "layers/utilities/python/tests/test_xml_helper.py::TestACDEventXMLParser::test_xml_to_json_acd_login_event", "layers/utilities/python/tests/test_xml_helper.py::TestACDEventXMLParser::test_xml_to_json_complex_nested_structure", "layers/utilities/python/tests/test_xml_helper.py::TestACDEventXMLParser::test_xml_to_json_empty_xml", "layers/utilities/python/tests/test_xml_helper.py::TestACDEventXMLParser::test_xml_to_json_invalid_xml", "layers/utilities/python/tests/test_xml_helper.py::TestACDEventXMLParser::test_xml_to_json_logging", "layers/utilities/python/tests/test_xml_helper.py::TestACDEventXMLParser::test_xml_to_json_login_event", "layers/utilities/python/tests/test_xml_helper.py::TestACDEventXMLParser::test_xml_to_json_logout_event", "layers/utilities/python/tests/test_xml_helper.py::TestACDEventXMLParser::test_xml_to_json_malformed_xml", "layers/utilities/python/tests/test_xml_helper.py::TestACDEventXMLParser::test_xml_to_json_special_characters", "layers/utilities/python/tests/test_xml_helper.py::TestACDEventXMLParser::test_xml_to_json_unicode_characters", "layers/utilities/python/tests/test_xml_helper.py::TestACDEventXMLParser::test_xml_to_json_with_attributes", "layers/utilities/python/tests/test_xml_helper.py::TestACDEventXMLParser::test_xml_to_json_xml_with_cdata", "layers/utilities/python/tests/test_xml_helper.py::TestACDEventXMLParser::test_xml_to_json_xml_with_namespaces", "tests/unit/test_event_processor.py::TestEventProcessor::test_transform_agent_busied_out_event_to_fact_data", "tests/unit/test_event_processor.py::TestEventProcessor::test_transform_login_event_to_fact_data", "tests/unit/test_event_processor.py::TestEventProcessor::test_transform_logout_event_with_voice_qos", "tests/unit/test_event_processor.py::TestEventProcessor::test_transform_queue_state_change_event", "tests/unit/test_xml_parser.py::TestAgentEventXMLParser::test_extract_xml_from_raw_sqs_message", "tests/unit/test_xml_parser.py::TestAgentEventXMLParser::test_extract_xml_from_sqs_json_message", "tests/unit/test_xml_parser.py::TestAgentEventXMLParser::test_parse_acd_login_event", "tests/unit/test_xml_parser.py::TestAgentEventXMLParser::test_parse_agent_available_event", "tests/unit/test_xml_parser.py::TestAgentEventXMLParser::test_parse_agent_busied_out_event", "tests/unit/test_xml_parser.py::TestAgentEventXMLParser::test_parse_invalid_xml", "tests/unit/test_xml_parser.py::TestAgentEventXMLParser::test_parse_login_event", "tests/unit/test_xml_parser.py::TestAgentEventXMLParser::test_parse_logout_event_with_voice_qos", "tests/unit/test_xml_parser.py::TestAgentEventXMLParser::test_parse_queue_state_change_event", "tests/unit/test_xml_parser.py::TestAgentEventXMLParser::test_parse_unknown_event_type", "tests/unit/test_xml_parser.py::TestAgentEventXMLParser::test_parse_xml_missing_required_fields"]