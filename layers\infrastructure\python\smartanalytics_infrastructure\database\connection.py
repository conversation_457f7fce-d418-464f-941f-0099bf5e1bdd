"""Database connection management for Aurora PostgreSQL with RDS Proxy support."""

from functools import lru_cache

import boto3
from mypy_boto3_rds.client import RDSClient
from smartanalytics_utilities.config.database_config import DatabaseConfig
from smartanalytics_utilities.config.settings import Settings
from smartanalytics_utilities.utils.logging_helper import get_logger
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncEngine, create_async_engine
from sqlalchemy.pool import NullPool

# Global RDS client for IAM token generation (reuse across invocations)
_rds_client: RDSClient | None = None


class DatabaseConnection:
    """Database connection manager for Aurora PostgreSQL with RDS Proxy."""

    def __init__(self, config: DatabaseConfig | None = None, region: str | None = None):
        """Initialize database connection.

        Args:
            config: Database configuration. If not provided, loads from settings.
            region: AWS region. If not provided, uses AWS_REGION env var or defaults to us-east-1.
        """
        super().__init__()

        self.settings = Settings()
        self.region = region or self.settings.aws.region
        if config is None:
            config = self.settings.database
        self.config = config
        self._engine: AsyncEngine | None = None

        self.logger = get_logger(service_name="smartanalytics-database")

        # Initialize RDS client for IAM token generation if needed
        if self.config.use_iam_auth:
            self._init_rds_client()

    def _init_rds_client(self) -> None:
        """Initialize RDS client for IAM token generation."""
        global _rds_client
        if _rds_client is None:
            _rds_client = boto3.client("rds", region_name=self.region)
            self.logger.info(
                "Initialized RDS client for IAM authentication",
                region=self.region,
            )

    def _generate_iam_token(self) -> str:
        """Generate IAM authentication token for RDS Proxy.

        Returns:
            15-minute IAM authentication token
        """
        if _rds_client is None:
            self._init_rds_client()

        if not self.config.username:
            raise ValueError("Username must be set to generate IAM token")

        if _rds_client is None:
            raise RuntimeError(
                "RDS client not initialized. Call _initialize_rds_client() first."
            )

        token = _rds_client.generate_db_auth_token(
            DBHostname=self.config.host,
            Port=self.config.port,
            DBUsername=self.config.username,
            Region=self.region,
        )

        self.logger.info(
            "Generated IAM authentication token",
            host=self.config.host,
            port=self.config.port,
            username=self.config.username,
            region=self.region,
        )

        return token

    def get_engine(self) -> AsyncEngine:
        """Get or create async database engine.

        Returns:
            Configured async SQLAlchemy engine
        """
        if self._engine is None:
            self._engine = self._create_engine()

        return self._engine

    def _create_engine(self) -> AsyncEngine:
        """Create async database engine with optimal configuration.

        Returns:
            Configured async SQLAlchemy engine
        """
        # Generate IAM token if using IAM authentication
        iam_token = None
        if self.config.use_iam_auth:
            iam_token = self._generate_iam_token()

        # Build connection URL (with IAM token if applicable)
        connection_url = self.config.get_connection_url(iam_token=iam_token)

        # Configure connection parameters
        connect_args = self.config.get_connection_params()

        # Configure pooling based on environment
        engine_kwargs = {
            "echo": self.config.echo_sql,
            "echo_pool": False,  # Disable pool echo for production
            "connect_args": connect_args,
            "query_cache_size": 1200 if self.config.enable_query_cache else 0,
            "future": True,
        }

        # Each Lambda invocation gets a fresh connection
        pool_class_name = "NullPool"
        pool_size_value = 0
        engine_kwargs.update(
            {
                "poolclass": NullPool,
            }
        )

        # Create engine
        engine = create_async_engine(connection_url, **engine_kwargs)

        self.logger.info(
            "Created database engine",
            host=self.config.host,
            port=self.config.port,
            database=self.config.database_name,
            schema=self.config.schema_name,
            pool_class=pool_class_name,
            pool_size=pool_size_value,
            use_iam_auth=self.config.use_iam_auth,
        )

        return engine

    async def test_connection(self) -> bool:
        """Test database connection.

        Returns:
            True if connection successful, False otherwise
        """
        try:
            engine = self.get_engine()

            async with engine.begin() as conn:
                result = await conn.execute(text("SELECT 1"))
                row = result.fetchone()

                if row and row[0] == 1:
                    self.logger.debug("Database connection test successful")
                    return True

                self.logger.error("Database connection test failed: unexpected result")
                return False

        except Exception as e:
            self.logger.error(f"Database connection test failed: {e}")
            return False

    async def close(self) -> None:
        """Close database engine and all connections."""
        if self._engine:
            await self._engine.dispose()
            self._engine = None
            self.logger.info("Database engine closed")


@lru_cache
def get_database_connection() -> DatabaseConnection:
    """Get cached database connection instance.

    Returns:
        Singleton database connection instance
    """
    return DatabaseConnection()
