#!/usr/bin/env python3
"""
Professional Integration Test for ACD Event Processor Lambda.

This script tests the Lambda function against a real Aurora PostgreSQL database.
It supports testing individual event types or all events at once.

Usage:
    python integration_test.py --all                    # Test all event types
    python integration_test.py --event AgentAvailable   # Test specific event
    python integration_test.py --list                   # List available events
    python integration_test.py --help                   # Show help

Requirements:
    - Aurora PostgreSQL database accessible
    - Environment variables set (or use .env file)
    - Test schema 'test' will be created/recreated automatically using SQLAlchemy models
"""

import argparse
import asyncio
import json
import os
import sys
from datetime import UTC, datetime
from typing import Any

from dotenv import load_dotenv
from smartanalytics_infrastructure.database.connection import DatabaseConnection
from smartanalytics_infrastructure.database.models import Base
from sqlalchemy import text
from sqlalchemy.exc import ProgrammingError

from src.lambda_function import lambda_handler

load_dotenv()


class MockLambdaContext:
    """Mock Lambda context for testing with all required AWS Lambda Powertools attributes."""

    def __init__(self):
        self.function_name = "acd-event-processor-test"
        self.function_version = "$LATEST"
        self.invoked_function_arn = (
            "arn:aws:lambda:us-east-1:123456789012:function:acd-event-processor-test"
        )
        self.memory_limit_in_mb = 512
        self.request_id = "test-request-id-12345"
        self.aws_request_id = "test-request-id-12345"
        self.log_group_name = "/aws/lambda/acd-event-processor-test"
        self.log_stream_name = "2025/01/15/[$LATEST]test-stream"
        self.identity = None
        self.client_context = None

    def get_remaining_time_in_millis(self):
        return 300000  # 5 minutes


# Event templates with placeholders for dynamic data
EVENT_TEMPLATES = {
    "Login": """<LogEvent xmlns="http://solacom.com/Logging">
    <timestamp>{timestamp}</timestamp>
    <agencyOrElement>{tenant}</agencyOrElement>
    <agent>{agent}</agent>
    <eventType>Login</eventType>
    <login>
        <mediaLabel>_ML_{unique_id}@{tenant}</mediaLabel>
        <uri>tel:+{phone}</uri>
        <agentRole>{agent_role}</agentRole>
        <tenantGroup>{tenant}</tenantGroup>
        <operatorId>{operator_id}</operatorId>
        <workstation>{workstation}</workstation>
        <deviceName>Headset</deviceName>
        <reason>normal</reason>
    </login>
</LogEvent>""",
    "Logout": """<LogEvent xmlns="http://solacom.com/Logging">
    <timestamp>{timestamp}</timestamp>
    <agencyOrElement>{tenant}</agencyOrElement>
    <agent>{agent}</agent>
    <eventType>Logout</eventType>
    <logout>
        <mediaLabel>_ML_{unique_id}@{tenant}</mediaLabel>
        <uri>tel:+{phone}</uri>
        <agentRole>{agent_role}</agentRole>
        <tenantGroup>{tenant}</tenantGroup>
        <operatorId>{operator_id}</operatorId>
        <workstation>{workstation}</workstation>
        <deviceName>Headset</deviceName>
        <reason>normal</reason>
        <responseCode>16</responseCode>
        <voiceQOS>
            <mediaIpSourceAddr>*************</mediaIpSourceAddr>
            <mediaIpDestAddr>**************</mediaIpDestAddr>
            <mediaUdpRtpSourcePort>21464</mediaUdpRtpSourcePort>
            <mediaUdpRtpDestPort>4000</mediaUdpRtpDestPort>
            <mediaNumOfIpPktRxed>65535</mediaNumOfIpPktRxed>
            <mediaNumOfIpPktTxed>65535</mediaNumOfIpPktTxed>
            <mediaNumOfIpErroredPktRxed>65535</mediaNumOfIpErroredPktRxed>
            <mediaNumOfRtpPktRxed>2018682</mediaNumOfRtpPktRxed>
            <mediaNumOfRtpPktTxed>2171890</mediaNumOfRtpPktTxed>
            <mediaNumOfRtpPktLost>0</mediaNumOfRtpPktLost>
            <mediaNumOfRtpPktDiscarded>0</mediaNumOfRtpPktDiscarded>
            <mediaRtpJitter>6</mediaRtpJitter>
            <mediaRtpLatency>1</mediaRtpLatency>
            <mediaNumOfRtcpPktRxed>9444</mediaNumOfRtcpPktRxed>
            <mediaNumOfRtcpPktTxed>17370</mediaNumOfRtcpPktTxed>
            <mediaFarEndPacketLostPercentage>0</mediaFarEndPacketLostPercentage>
            <mediaFarEndCumulativePacketLost>0</mediaFarEndCumulativePacketLost>
            <mediaFarEndInterarrivalJitter>2</mediaFarEndInterarrivalJitter>
        </voiceQOS>
    </logout>
</LogEvent>""",
    "AgentAvailable": """<LogEvent xmlns="http://solacom.com/Logging">
    <timestamp>{timestamp}</timestamp>
    <agencyOrElement>{tenant}</agencyOrElement>
    <agent>{agent}</agent>
    <eventType>AgentAvailable</eventType>
    <agentAvailable>
        <mediaLabel>_ML_{unique_id}@{tenant}</mediaLabel>
        <uri>tel:+{phone}</uri>
        <agentRole>{agent_role}</agentRole>
        <tenantGroup>{tenant}</tenantGroup>
        <operatorId>{operator_id}</operatorId>
        <workstation>{workstation}</workstation>
        <busiedOutAction>Manual</busiedOutAction>
    </agentAvailable>
</LogEvent>""",
    "AgentBusiedOut": """<LogEvent xmlns="http://solacom.com/Logging">
    <timestamp>{timestamp}</timestamp>
    <agencyOrElement>{tenant}</agencyOrElement>
    <agent>{agent}</agent>
    <eventType>AgentBusiedOut</eventType>
    <busiedOut>
        <mediaLabel>_ML_{unique_id}@{tenant}</mediaLabel>
        <uri>tel:+{phone}</uri>
        <agentRole>{agent_role}</agentRole>
        <tenantGroup>{tenant}</tenantGroup>
        <operatorId>{operator_id}</operatorId>
        <workstation>{workstation}</workstation>
        <busiedOutAction>Break</busiedOutAction>
    </busiedOut>
</LogEvent>""",
    "ACDLogin": """<LogEvent xmlns="http://solacom.com/Logging">
    <timestamp>{timestamp}</timestamp>
    <agencyOrElement>{tenant}</agencyOrElement>
    <agent>{agent}</agent>
    <eventType>ACDLogin</eventType>
    <acdLogin>
        <mediaLabel>_ML_{unique_id}@{tenant}</mediaLabel>
        <agentUri>tel:+{phone}</agentUri>
        <agentRole>{agent_role}</agentRole>
        <tenantGroup>{tenant}</tenantGroup>
        <operatorId>{operator_id}</operatorId>
        <workstation>{workstation}</workstation>
        <deviceName>Headset</deviceName>
        <ringGroupName>{ring_group}</ringGroupName>
        <ringGroupUri>tel:+{ring_group_phone}</ringGroupUri>
    </acdLogin>
</LogEvent>""",
    "ACDLogout": """<LogEvent xmlns="http://solacom.com/Logging">
    <timestamp>{timestamp}</timestamp>
    <agencyOrElement>{tenant}</agencyOrElement>
    <agent>{agent}</agent>
    <eventType>ACDLogout</eventType>
    <acdLogout>
        <mediaLabel>_ML_{unique_id}@{tenant}</mediaLabel>
        <agentUri>tel:+{phone}</agentUri>
        <agentRole>{agent_role}</agentRole>
        <tenantGroup>{tenant}</tenantGroup>
        <operatorId>{operator_id}</operatorId>
        <workstation>{workstation}</workstation>
        <deviceName>Headset</deviceName>
        <ringGroupName>{ring_group}</ringGroupName>
        <ringGroupUri>tel:+{ring_group_phone}</ringGroupUri>
    </acdLogout>
</LogEvent>""",
}


def generate_event_xml(event_type: str, sequence: int = 1) -> str:
    """Generate XML for an event with unique data to avoid duplicates."""
    template = EVENT_TEMPLATES.get(event_type)
    if not template:
        raise ValueError(f"Unknown event type: {event_type}")

    now = datetime.now(UTC)
    timestamp = now.isoformat().replace("+00:00", "Z")
    unique_id = f"{int(now.timestamp() * 1000)}{sequence:04d}"

    # Fill in template with production-like data
    return template.format(
        timestamp=timestamp,
        tenant=f"TestTenant{sequence}",
        agent=f"agent{sequence:03d}",
        agent_role=f"TestRole{sequence}",
        ring_group=f"RingGroup{sequence}",
        unique_id=unique_id,
        operator_id=str(sequence),
        workstation=f"WS{sequence:02d}",
        phone=f"204555{3000 + sequence}",
        ring_group_phone=f"204555{4000 + sequence}",
        duration=str(300 + sequence),
        reason_code=str(100 + sequence),
    )


def create_sqs_event(xml_content: str, message_id: str | None = None) -> dict[str, Any]:
    """Create a mock SQS event for testing."""
    now = datetime.now(UTC)
    timestamp_ms = int(now.timestamp() * 1000)

    if message_id is None:
        message_id = f"msg-{timestamp_ms}"

    return {
        "Records": [
            {
                "messageId": message_id,
                "receiptHandle": "test-receipt-handle",
                "body": xml_content,
                "attributes": {
                    "ApproximateReceiveCount": "1",
                    "SentTimestamp": str(timestamp_ms),
                    "SenderId": "test-sender",
                    "ApproximateFirstReceiveTimestamp": str(timestamp_ms),
                },
                "messageAttributes": {},
                "md5OfBody": "test-md5",
                "eventSource": "aws:sqs",
                "eventSourceARN": "arn:aws:sqs:us-east-1:123456789012:test-queue",
                "awsRegion": "us-east-1",
            }
        ]
    }


async def recreate_schema():
    """Recreate database schema for testing."""
    print("\n" + "=" * 80)
    print("RECREATING DATABASE SCHEMA")
    print("=" * 80)

    db_conn = DatabaseConnection()
    engine = db_conn.get_engine()

    # Hardcode test schema for integration testing
    schema_name = "test"
    print(f"Using schema: {schema_name}")

    try:
        async with engine.begin() as conn:
            # Drop and recreate test schema
            print("Dropping existing schema...")
            await conn.execute(text(f"DROP SCHEMA IF EXISTS {schema_name} CASCADE"))

            print("Creating schema...")
            await conn.execute(text(f"CREATE SCHEMA {schema_name}"))

            # Create all tables using SQLAlchemy models
            print("Creating tables...")
            # Set the schema for all tables in the metadata
            for table in Base.metadata.tables.values():
                table.schema = schema_name

            # Create all tables using SQLAlchemy
            await conn.run_sync(Base.metadata.create_all)
        print(" Schema recreated successfully\n")
    finally:
        await engine.dispose()


async def verify_database_content():
    """Verify database content (optional - only if tables exist)."""
    print("\n" + "=" * 80)
    print("VERIFYING DATABASE CONTENT")
    print("=" * 80)

    db_conn = DatabaseConnection()
    engine = db_conn.get_engine()

    # Hardcode test schema for integration testing
    schema_name = "test"

    try:
        async with engine.begin() as conn:
            # Check if table exists first
            table_check = await conn.execute(
                text(
                    "SELECT EXISTS ("
                    "SELECT FROM information_schema.tables "
                    "WHERE table_schema = :schema_name "
                    "AND table_name = 'dim_agent_event'"
                    ")"
                ),
                {"schema_name": schema_name},
            )
            table_exists = table_check.scalar()

            if not table_exists:
                # Table doesn't exist - skip verification silently
                print("  Skipping verification - table does not exist")
                return

            # Check events using parameterized query for schema
            base_query = (
                "SELECT event_key, event_type, agent_key, ring_group_key, tenant_key, "
                "operator_id, workstation, media_label, uri, device_name, "
                "busied_out_action, busied_out_duration, reason_code, "
                "event_data->>'eventType' as event_type_from_json "
                "FROM test.dim_agent_event ORDER BY event_key"
            )
            result = await conn.execute(text(base_query))
            events = result.fetchall()

            print(f"\nFound {len(events)} events in database:")
            for event in events:
                print(f"\n  Event {event[0]}: {event[1]}")
                print(
                    f"    - agent_key: {event[2]}, ring_group_key: {event[3]}, tenant_key: {event[4]}"
                )
                print(f"    - operator_id: {event[5]}, workstation: {event[6]}")
                print(f"    - media_label: {event[7]}")
                print(f"    - uri: {event[8]}, device_name: {event[9]}")
                print(
                    f"    - busied_out_action: {event[10]}, busied_out_duration: {event[11]}"
                )
                print(f"    - reason_code: {event[12]}")
                print(f"    - event_data contains: {event[13]}")

    except ProgrammingError as e:
        print(f"\n  Could not verify database content: {e}")
        print("   This is expected if tables don't exist yet.")
    finally:
        await engine.dispose()


def check_event(event_type: str, sequence: int = 1) -> bool:
    """Test a single event type."""
    print(f"\n{sequence}. Testing {event_type}...")

    # Generate unique XML
    xml_content = generate_event_xml(event_type, sequence)

    # Create SQS event
    sqs_event = create_sqs_event(xml_content)

    # Create context
    context = MockLambdaContext()

    # Call Lambda handler
    try:
        result = lambda_handler(sqs_event, context)  # type: ignore[misc]

        # Check result
        if result.get("statusCode") == 200:
            body = json.loads(result["body"])
            print(f"   Success - Processed: {body.get('processed_events', 0)}")
            return True
        print(f"   Failed - Status: {result.get('statusCode')}")
        print(f"      Error: {result.get('body')}")
        return False

    except Exception as e:
        print(f"   Exception: {e}")
        return False


def main():
    """Main entry point."""
    args = parse_arguments()

    if args.list:
        list_event_types()
        return 0

    if not validate_arguments(args):
        return 1

    if not validate_environment():
        return 1

    # Override schema to use 'test' for integration testing
    original_schema = os.environ.get("DATABASE_SCHEMA_NAME")
    os.environ["DATABASE_SCHEMA_NAME"] = "test"

    try:
        if not args.no_recreate:
            asyncio.run(recreate_schema())

        success = run_tests(args)

        if not args.no_verify:
            asyncio.run(verify_database_content())

        print_final_result(success)
        return 0 if success else 1
    finally:
        # Restore original schema
        if original_schema is not None:
            os.environ["DATABASE_SCHEMA_NAME"] = original_schema
        else:
            os.environ.pop("DATABASE_SCHEMA_NAME", None)


def parse_arguments():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(
        description="Integration test for ACD Event Processor Lambda",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__,
    )
    parser.add_argument("--all", action="store_true", help="Test all event types")
    parser.add_argument("--event", type=str, help="Test specific event type")
    parser.add_argument(
        "--list", action="store_true", help="List available event types"
    )
    parser.add_argument(
        "--no-recreate", action="store_true", help="Skip schema recreation"
    )
    parser.add_argument(
        "--no-verify", action="store_true", help="Skip database verification"
    )
    return parser.parse_args()


def list_event_types():
    """List available event types."""
    print("\nAvailable event types:")
    for event_type in EVENT_TEMPLATES:
        print(f"  - {event_type}")


def validate_arguments(args):
    """Validate command-line arguments."""
    if not args.all and not args.event:
        print("Error: Either --all or --event must be specified.")
        return False

    if args.event and args.event not in EVENT_TEMPLATES:
        print(f"\nError: Unknown event type '{args.event}'")
        list_event_types()
        return False

    return True


def validate_environment():
    """Validate required environment variables."""
    required_vars = [
        "DATABASE__HOST",
        "DATABASE__DATABASE_NAME",
        "DATABASE__USERNAME",
        "DATABASE__SCHEMA_NAME",
    ]

    missing_vars = [var for var in required_vars if not os.environ.get(var)]
    if missing_vars:
        print(f"\n Missing required environment variables: {', '.join(missing_vars)}")
        return False

    password = os.environ.get("DATABASE__PASSWORD")
    use_iam = os.environ.get("DATABASE__USE_IAM_AUTH", "false").lower() == "true"

    if not use_iam and not password:
        print("\n No valid database password provided and IAM auth not enabled.")
        return False

    print("\n Database configuration:")
    print(f"   Host: {os.environ.get('DATABASE__HOST')}")
    print(f"   Database: {os.environ.get('DATABASE__DATABASE_NAME')}")
    print(f"   Schema: {os.environ.get('DATABASE__SCHEMA_NAME')}")
    print(f"   Username: {os.environ.get('DATABASE__USERNAME')}")
    print(f"   Auth Method: {'IAM' if use_iam else 'Password'}")
    print(f"   SSL Mode: {os.environ.get('DATABASE__SSL_MODE', 'require')}")
    return True


def run_tests(args):
    """Run the tests based on the arguments."""
    print("\n" + "=" * 80)
    print("RUNNING TESTS")
    print("=" * 80)

    if args.all:
        results = {
            event_type: check_event(event_type, i)
            for i, event_type in enumerate(EVENT_TEMPLATES.keys(), 1)
        }
        print_test_summary(results)
        return all(results.values())
    return check_event(args.event, 1)


def print_test_summary(results):
    """Print a summary of the test results."""
    print("\n" + "=" * 80)
    print("TEST SUMMARY")
    print("=" * 80)
    passed = sum(1 for v in results.values() if v)
    total = len(results)
    print(f"\nPassed: {passed}/{total}")
    for event_type, success in results.items():
        status = "PASS" if success else "FAIL"
        print(f"  {status} {event_type}")


def print_final_result(success):
    """Print the final result of the tests."""
    print("\n" + "=" * 80)
    if success:
        print(" ALL TESTS PASSED")
    else:
        print(" SOME TESTS FAILED")
    print("=" * 80 + "\n")


if __name__ == "__main__":
    sys.exit(main())
