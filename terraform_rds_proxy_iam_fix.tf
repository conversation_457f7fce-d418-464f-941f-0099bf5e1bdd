# RDS Proxy IAM Authentication Fix
# 
# This file contains the Terraform configuration changes needed to fix
# IAM authentication with RDS Proxy.
#
# Problem: RDS Proxy IAM auth fails because the secret contains master user 
# credentials instead of the IAM user credentials.
#
# Solution: Create a separate secret for IAM authentication with common_user

# Create a new secret specifically for IAM authentication
resource "aws_secretsmanager_secret" "aurora_db_credentials_iam" {
  name                    = "${local.prefix}-aurora-credentials-iam"
  description             = "Aurora database credentials for IAM authentication via RDS Proxy"
  recovery_window_in_days = 7

  tags = {
    Name = "${local.prefix}-aurora-credentials-iam"
  }
}

# Store the IAM user credentials in the secret
resource "aws_secretsmanager_secret_version" "aurora_db_credentials_iam" {
  secret_id = aws_secretsmanager_secret.aurora_db_credentials_iam.id
  secret_string = jsonencode({
    username = "common_user"
    # No password needed for IAM authentication - RDS Proxy will use IAM tokens
    # But we need to include a dummy password to satisfy the secret format
    password = "not-used-for-iam-auth"
  })
}

# Updated RDS Proxy configuration with correct IAM auth secret
resource "aws_db_proxy" "aurora_db_proxy" {
  name                   = "${local.prefix}-aurora-db-proxy"
  engine_family          = "POSTGRESQL"
  role_arn               = aws_iam_role.aurora_db_proxy_role.arn
  debug_logging          = false
  require_tls            = true
  idle_client_timeout    = var.aurora_idle_client_timeout
  vpc_subnet_ids         = var.aurora_private_subnet_ids
  vpc_security_group_ids = [aws_security_group.aurora_db_proxy_security_group.id]

  # Password authentication using master user credentials
  auth {
    auth_scheme = "SECRETS"
    description = "RDS Proxy Password Auth"
    iam_auth    = "DISABLED"
    secret_arn  = aws_secretsmanager_secret.aurora_db_credentials.arn
  }

  # IAM authentication using common_user credentials
  auth {
    auth_scheme = "SECRETS"
    description = "RDS Proxy IAM Auth"
    iam_auth    = "REQUIRED"
    secret_arn  = aws_secretsmanager_secret.aurora_db_credentials_iam.arn
  }

  timeouts {
    create = var.aurora_proxy_create_timeout
    update = var.aurora_proxy_update_timeout
    delete = var.aurora_proxy_delete_timeout
  }

  tags = {
    Name = "${local.prefix}-aurora-db-proxy"
  }
}

# Grant the proxy role access to the new IAM secret
resource "aws_secretsmanager_secret_policy" "aurora_db_credentials_iam_policy" {
  secret_arn = aws_secretsmanager_secret.aurora_db_credentials_iam.arn

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          AWS = aws_iam_role.aurora_db_proxy_role.arn
        }
        Action   = "secretsmanager:GetSecretValue"
        Resource = "*"
      }
    ]
  })
}

# Update the proxy role policy to include access to the new secret
data "aws_iam_policy_document" "aurora_db_proxy_policy_updated" {
  statement {
    effect = "Allow"
    actions = [
      "secretsmanager:GetSecretValue",
      "secretsmanager:DescribeSecret"
    ]
    resources = [
      aws_secretsmanager_secret.aurora_db_credentials.arn,
      aws_secretsmanager_secret.aurora_db_credentials_for_db_proxy.arn,
      aws_secretsmanager_secret.aurora_db_credentials_iam.arn
    ]
  }

  statement {
    effect = "Allow"
    actions = [
      "kms:Decrypt"
    ]
    resources = [
      var.kms_key_id
    ]
    condition {
      test     = "StringEquals"
      variable = "kms:ViaService"
      values   = ["secretsmanager.${data.aws_region.current.name}.amazonaws.com"]
    }
  }
}

# Output the new secret ARN for reference
output "aurora_db_credentials_iam_secret_arn" {
  description = "ARN of the Aurora database credentials secret for IAM authentication"
  value       = aws_secretsmanager_secret.aurora_db_credentials_iam.arn
}
