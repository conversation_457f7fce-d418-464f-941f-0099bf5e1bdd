# Updated IAM Configuration for RDS Proxy IAM Authentication

## Summary of Changes Made

I've updated your Terraform IAM configuration in `terraform/modules/acd-processor/iam.tf` to properly support RDS Proxy IAM authentication.

## Key Changes Applied

### 1. Enhanced RDS Permissions
**Added comprehensive RDS describe permissions** required for IAM token generation:

```hcl
# RDS Describe permissions for IAM token generation and cluster information
statement {
  sid    = "RDSDescribe"
  effect = "Allow"
  actions = [
    "rds:DescribeDBClusters",
    "rds:DescribeDBInstances", 
    "rds:DescribeDBProxies",
    "rds:DescribeDBProxyTargets"
  ]
  resources = ["*"]
}
```

### 2. Cleaned Up Duplicate Permissions
- Removed duplicate RDS describe statements
- Consolidated permissions for better maintainability

### 3. Existing Correct Permissions
Your configuration already had these correct permissions:

✅ **RDS Connect Permission**:
```hcl
statement {
  sid    = "RDSConnect"
  effect = "Allow"
  actions = ["rds-db:connect"]
  resources = [
    "arn:aws:rds-db:${region}:${account}:dbuser:${var.rds_cluster_resource_id}/${var.database_user_name}"
  ]
}
```

✅ **Secrets Manager Access**:
```hcl
statement {
  sid    = "SecretRead"
  effect = "Allow"
  actions = ["secretsmanager:GetSecretValue"]
  resources = [
    "arn:aws:secretsmanager:${region}:${account}:secret:${var.database_secret_name}*"
  ]
}
```

✅ **KMS Permissions**:
```hcl
statement {
  sid    = "KMSDecryptForReads"
  effect = "Allow"
  actions = [
    "kms:Decrypt",
    "kms:GenerateDataKey",
    "kms:GenerateDataKeyWithoutPlaintext"
  ]
  resources = [
    "arn:aws:kms:${region}:${account}:key/${var.kms_key_name}",
    "arn:aws:kms:${region}:${account}:alias/${var.kms_key_name}"
  ]
}
```

## Required Terragrunt Configuration Updates

### Update Secret Name in Terragrunt
In your `terragrunt.hcl` file, change:

```diff
# Database configuration (Aurora via RDS Proxy)
- database_secret_name = "dev-au-smartanalytics-common-aurora-common-role-credentials"
+ database_secret_name = "dev-au-smartanalytics-common-aurora-credentials-for-db-proxy"
```

## Complete IAM Policy Summary

Your Lambda will have these permissions for RDS Proxy IAM authentication:

1. **🔐 RDS IAM Authentication**:
   - `rds-db:connect` on the specific proxy/user combination
   - `rds:DescribeDBProxies` for token generation

2. **🔑 Secrets Manager**:
   - `secretsmanager:GetSecretValue` on the IAM auth secret

3. **🔒 KMS**:
   - `kms:Decrypt` for encrypted secrets

4. **🌐 VPC**:
   - Standard VPC Lambda permissions for network access

## Verification Steps

### 1. Check IAM Resource ARN Format
Ensure your `rds_cluster_resource_id` in Terragrunt is correct:
```hcl
rds_cluster_resource_id = "prx-003e87a11a1e672f2"  # ✅ Correct proxy resource ID
```

### 2. Verify Database User Name
```hcl
database_user_name = "common_user"  # ✅ Matches the IAM auth secret
```

### 3. Environment Variables
Your environment variables are correctly configured:
```hcl
environment_variables = {
  DATABASE__HOST          = "dev-au-smartanalytics-common-aurora-db-proxy.proxy-c3atdbtyiqrw.ap-southeast-2.rds.amazonaws.com"
  DATABASE__USERNAME      = "common_user"
  DATABASE__USE_IAM_AUTH  = true
  # ... other vars
}
```

## Expected IAM Policy Result

When deployed, your Lambda will have an IAM policy that allows:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "RDSConnect",
      "Effect": "Allow",
      "Action": "rds-db:connect",
      "Resource": "arn:aws:rds-db:ap-southeast-2:024660257967:dbuser:prx-003e87a11a1e672f2/common_user"
    },
    {
      "Sid": "RDSDescribe", 
      "Effect": "Allow",
      "Action": [
        "rds:DescribeDBClusters",
        "rds:DescribeDBProxies"
      ],
      "Resource": "*"
    },
    {
      "Sid": "SecretRead",
      "Effect": "Allow", 
      "Action": "secretsmanager:GetSecretValue",
      "Resource": "arn:aws:secretsmanager:ap-southeast-2:024660257967:secret:dev-au-smartanalytics-common-aurora-credentials-for-db-proxy*"
    }
  ]
}
```

## Next Steps

1. **Update Terragrunt**: Change the `database_secret_name` as shown above
2. **Deploy**: Run `terragrunt apply` to update the Lambda IAM role
3. **Test**: Deploy your Lambda and test the RDS Proxy IAM connection

The IAM configuration is now properly set up for RDS Proxy IAM authentication! 🎉
