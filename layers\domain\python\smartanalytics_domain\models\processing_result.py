"""Processing result models for typed responses."""

from dataclasses import dataclass


@dataclass(frozen=True)
class DimensionKeys:
    """Dimension keys for an event."""

    tenant_key: int
    agent_key: int
    ring_group_key: int | None = None


@dataclass(frozen=True)
class SingleEventResult:
    """Result of processing a single event."""

    success: bool
    event_hash: str | None = None
    event_type: str | None = None
    is_duplicate: bool = False
    error_message: str | None = None
    processing_time_ms: float = 0.0


@dataclass(frozen=True)
class BatchEventResult:
    """Result of processing a batch of events."""

    success: bool
    batch_id: str
    total_events: int
    processed_events: int
    duplicate_events: int
    failed_events: int = 0
    event_hashes: list[str] | None = None
    tenant_ids: list[str] | None = None
    event_types: list[str] | None = None
    error_message: str | None = None
    processing_time_ms: float = 0.0
    failed_message_ids: list[str] | None = None
