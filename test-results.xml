<?xml version="1.0" encoding="utf-8"?><testsuites><testsuite name="pytest" errors="0" failures="0" skipped="0" tests="10" time="2.092" timestamp="2025-10-08T11:19:31.317148" hostname="HYB-d7HUHw58Q86"><testcase classname="tests.test_lambda_handler.TestProcessEventsAsync" name="test_process_single_event_success" time="0.019" /><testcase classname="tests.test_lambda_handler.TestProcessEventsAsync" name="test_process_single_event_duplicate" time="0.008" /><testcase classname="tests.test_lambda_handler.TestProcessEventsAsync" name="test_process_single_event_failure" time="0.022" /><testcase classname="tests.test_lambda_handler.TestProcessEventsAsync" name="test_process_batch_events" time="0.007" /><testcase classname="tests.test_lambda_handler.TestLambdaHandler" name="test_handler_success" time="0.009" /><testcase classname="tests.test_lambda_handler.TestLambdaHandler" name="test_handler_with_duplicates" time="0.010" /><testcase classname="tests.test_lambda_handler.TestLambdaHandler" name="test_handler_with_failures" time="0.017" /><testcase classname="tests.test_lambda_handler.TestLambdaHandler" name="test_handler_with_sns_wrapped_message" time="0.010" /><testcase classname="tests.test_lambda_handler.TestLambdaHandler" name="test_handler_with_exception" time="0.016" /><testcase classname="tests.test_lambda_handler.TestLambdaHandler" name="test_handler_with_multiple_records" time="0.005" /></testsuite></testsuites>