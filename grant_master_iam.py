#!/usr/bin/env python3
"""Grant rds_iam role to master user."""

import asyncio
import sys

import asyncpg


async def grant_master_iam():
    """Grant rds_iam role to master user."""
    print("🔧 Granting rds_iam role to master user...")
    
    # Connect to primary-1 (writer instance) using master credentials
    host = "dev-au-smartanalytics-common-aurora-primary-2.c3atdbtyiqrw.ap-southeast-2.rds.amazonaws.com"
    port = 5432
    database = "prod"
    username = "Allerium"
    password = "72bOkOq23DL3MuUa"
    
    print(f"   Host: {host}")
    print(f"   Database: {database}")
    print(f"   Username: {username}")
    
    try:
        # Connect using master credentials
        print("   Connecting with master credentials...")
        conn = await asyncpg.connect(
            host=host,
            port=port,
            database=database,
            user=username,
            password=password,
            ssl="require"
        )
        
        print("✅ Connected successfully!")
        
        # Check if master user already has rds_iam role
        has_iam_role = await conn.fetchval(
            "SELECT 1 FROM pg_auth_members m "
            "JOIN pg_roles r ON m.roleid = r.oid "
            "JOIN pg_roles u ON m.member = u.oid "
            "WHERE r.rolname = 'rds_iam' AND u.rolname = $1",
            username
        )
        
        if has_iam_role:
            print(f"✅ Master user {username} already has rds_iam role")
        else:
            print(f"   Granting rds_iam role to {username}...")
            await conn.execute(f'GRANT rds_iam TO "{username}"')
            print(f"✅ Granted rds_iam role to {username}")
        
        # Verify the grant
        has_iam_role_after = await conn.fetchval(
            "SELECT 1 FROM pg_auth_members m "
            "JOIN pg_roles r ON m.roleid = r.oid "
            "JOIN pg_roles u ON m.member = u.oid "
            "WHERE r.rolname = 'rds_iam' AND u.rolname = $1",
            username
        )
        
        if has_iam_role_after:
            print(f"✅ Verified: {username} now has rds_iam role")
        else:
            print(f"❌ Failed to grant rds_iam role to {username}")
            return False
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Failed to grant rds_iam role: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Main function."""
    print("🚀 Granting rds_iam Role to Master User")
    print("=" * 50)
    
    success = await grant_master_iam()
    
    if success:
        print("\n✅ Master user now has rds_iam role!")
        print("   Ready to test IAM authentication.")
        return 0
    else:
        print("\n❌ Failed to grant rds_iam role to master user.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
