#!/usr/bin/env python3
"""Simple test of RDS Proxy IAM authentication."""

import asyncio
import sys

import asyncpg
import boto3


async def test_proxy_simple():
    """Test simple connection to RDS Proxy with IAM."""
    print("🔧 Testing simple RDS Proxy IAM authentication...")
    
    # Generate fresh IAM token
    rds_client = boto3.client("rds", region_name="ap-southeast-2")
    
    host = "dev-au-smartanalytics-common-aurora-db-proxy.proxy-c3atdbtyiqrw.ap-southeast-2.rds.amazonaws.com"
    port = 5432
    database = "prod"
    username = "common_user"
    
    print(f"   Host: {host}")
    print(f"   Database: {database}")
    print(f"   Username: {username}")
    
    try:
        # Generate fresh IAM token
        print("   Generating fresh IAM token...")
        iam_token = rds_client.generate_db_auth_token(
            DBHostname=host,
            Port=port,
            DBUsername=username,
            Region="ap-southeast-2"
        )
        
        print(f"✅ IAM token generated! Length: {len(iam_token)}")
        print(f"   Token preview: {iam_token[:50]}...")
        
        # Connect using IAM token with minimal settings
        print("   Connecting with IAM token...")
        conn = await asyncpg.connect(
            host=host,
            port=port,
            database=database,
            user=username,
            password=iam_token,
            ssl="require",
            command_timeout=30
        )
        
        print("✅ Connected successfully!")
        
        # Simple test query
        result = await conn.fetchval("SELECT 1")
        print(f"✅ Query successful! Result: {result}")
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Proxy IAM authentication failed: {e}")
        print(f"   Error type: {type(e).__name__}")
        return False


async def main():
    """Main function."""
    print("🚀 Testing Simple RDS Proxy IAM Authentication")
    print("=" * 60)
    
    success = await test_proxy_simple()
    
    if success:
        print("\n✅ RDS Proxy IAM authentication works!")
        print("   Your Lambda should work perfectly now.")
        return 0
    else:
        print("\n❌ RDS Proxy IAM authentication still failing.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
