"""Command handler for processing a batch of agent events."""

import time
import uuid

from smartanalytics_domain.command_handlers.process_agent_event_handler import (
    ProcessAgentEventCommandHandler,
)
from smartanalytics_domain.commands.process_agent_event_command import (
    ProcessAgentEventCommand,
)
from smartanalytics_domain.commands.process_agent_events_batch_command import (
    ProcessAgentEventsBatchCommand,
)
from smartanalytics_domain.models.processing_result import BatchEventResult
from smartanalytics_domain.ports.unit_of_work import UnitOfWork
from smartanalytics_utilities.utils.logging_helper import get_logger

logger = get_logger(__name__)


class ProcessAgentEventsBatchCommandHandler:
    """Handler for processing a batch of agent events command.

    This handler processes multiple events in a batch with optimizations:
    1. Caches dimension lookups (tenant, agent, ring group)
    2. Uses batch insert operations where possible
    3. Handles partial failures gracefully
    """

    def __init__(self, uow: UnitOfWork):
        """Initialize handler with unit of work.

        Args:
            uow: Unit of work for database operations
        """
        self.uow = uow
        self.single_event_handler = ProcessAgentEventCommandHandler(uow)

    async def handle(self, command: ProcessAgentEventsBatchCommand) -> BatchEventResult:
        """Handle the process agent events batch command.

        Args:
            command: Command containing list of XML contents and metadata

        Returns:
            BatchEventResult with processing statistics
        """
        start_time = time.time()
        batch_id = str(uuid.uuid4())

        logger.info(
            "Starting batch processing",
            batch_id=batch_id,
            batch_size=len(command.xml_contents),
        )

        processed_events = 0
        duplicate_events = 0
        failed_events = 0
        event_hashes: list[str] = []
        failed_message_ids: list[str] = []

        # Process each event individually
        # Note: We could optimize this further with true batch operations,
        # but processing individually provides better error isolation
        for i, xml_content in enumerate(command.xml_contents):
            try:
                single_command = ProcessAgentEventCommand(
                    xml_content=xml_content,
                    sqs_message_id=command.sqs_message_ids[i],
                    client_timezone=command.client_timezone,
                )

                result = await self.single_event_handler.handle(single_command)

                if result.success and result.event_hash:
                    if result.is_duplicate:
                        duplicate_events += 1
                    else:
                        processed_events += 1
                    event_hashes.append(result.event_hash)
                else:
                    failed_events += 1
                    failed_message_ids.append(command.sqs_message_ids[i])
                    logger.warning(
                        "Event processing failed in batch",
                        batch_id=batch_id,
                        event_index=i,
                        sqs_message_id=command.sqs_message_ids[i],
                        error=result.error_message,
                    )

            except Exception as e:
                failed_events += 1
                failed_message_ids.append(command.sqs_message_ids[i])
                logger.error(
                    "Unexpected error processing event in batch",
                    batch_id=batch_id,
                    event_index=i,
                    sqs_message_id=command.sqs_message_ids[i],
                    error=str(e),
                    error_type=type(e).__name__,
                )

        processing_time = (time.time() - start_time) * 1000

        logger.info(
            "Batch processing completed",
            batch_id=batch_id,
            total_events=len(command.xml_contents),
            processed_events=processed_events,
            duplicate_events=duplicate_events,
            failed_events=failed_events,
            processing_time_ms=processing_time,
        )

        return BatchEventResult(
            success=failed_events == 0,
            total_events=len(command.xml_contents),
            processed_events=processed_events,
            duplicate_events=duplicate_events,
            failed_events=failed_events,
            processing_time_ms=processing_time,
            batch_id=batch_id,
            event_hashes=event_hashes,
            failed_message_ids=failed_message_ids,
        )
