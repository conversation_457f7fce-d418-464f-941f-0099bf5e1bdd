#!/usr/bin/env python3
"""Test Lambda database connection."""

import asyncio
import os
import sys

# Add the layers to the path
sys.path.insert(0, "layers/infrastructure/python")
sys.path.insert(0, "layers/utilities/python")

from smartanalytics_infrastructure.database.connection import DatabaseConnection
from smartanalytics_utilities.config.settings import Settings


async def test_lambda_connection():
    """Test the Lambda database connection."""
    print("🚀 Testing Lambda Database Connection")
    print("=" * 50)
    
    # Set environment variables like Lambda would have
    os.environ["AWS_REGION"] = "ap-southeast-2"
    os.environ["AWS_DEFAULT_REGION"] = "ap-southeast-2"
    os.environ["POWERTOOLS_SERVICE_NAME"] = "acd-event-processor"
    os.environ["POWERTOOLS_LOG_LEVEL"] = "DEBUG"
    
    # AWS configuration
    os.environ["AWS__REGION"] = "ap-southeast-2"

    # Database configuration
    os.environ["DATABASE__HOST"] = "dev-au-smartanalytics-common-aurora-db-proxy.proxy-c3atdbtyiqrw.ap-southeast-2.rds.amazonaws.com"
    os.environ["DATABASE__PORT"] = "5432"
    os.environ["DATABASE__DATABASE_NAME"] = "prod"
    os.environ["DATABASE__USERNAME"] = "common_user"
    os.environ["DATABASE__USE_IAM_AUTH"] = "true"
    os.environ["DATABASE__SCHEMA_NAME"] = "common"
    
    try:
        print("🔧 Initializing database connection...")
        
        # Create database connection like the Lambda would
        db_connection = DatabaseConnection()
        
        print("   Database config:")
        print(f"   - Host: {db_connection.config.host}")
        print(f"   - Port: {db_connection.config.port}")
        print(f"   - Database: {db_connection.config.database_name}")
        print(f"   - Username: {db_connection.config.username}")
        print(f"   - Use IAM Auth: {db_connection.config.use_iam_auth}")
        print(f"   - Schema: {db_connection.config.schema_name}")
        print(f"   - Region: {db_connection.region}")
        
        print("\n🔑 Testing database connection...")
        
        # Test the connection using SQLAlchemy engine
        engine = db_connection.get_engine()

        async with engine.begin() as conn:
            # Simple test query
            from sqlalchemy import text
            result = await conn.execute(text("SELECT 1"))
            row = result.fetchone()
            print(f"✅ Connection successful! Test query result: {row[0] if row else None}")

            # Test schema access
            tables_result = await conn.execute(
                text("SELECT table_name FROM information_schema.tables WHERE table_schema = :schema"),
                {"schema": "common"}
            )
            tables = tables_result.fetchall()
            print(f"✅ Schema access successful! Found {len(tables)} tables in 'common' schema")

            if tables:
                print("   Tables:")
                for table in tables[:5]:  # Show first 5 tables
                    print(f"   - {table[0]}")
                if len(tables) > 5:
                    print(f"   ... and {len(tables) - 5} more")
        
        print("\n✅ Lambda database connection test successful!")
        return True
        
    except Exception as e:
        print(f"\n❌ Lambda database connection test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Main function."""
    success = await test_lambda_connection()
    return 0 if success else 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
